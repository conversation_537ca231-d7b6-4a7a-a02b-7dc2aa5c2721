# 本地Chrome MCP调试配置指南

## 系统架构
```
[本地Windows] ←→ [远程Ubuntu服务器 ************]
    Chrome调试        MES系统后端:3000
    MCP工具          React前端:3001
```

## 第一步：在本地Windows安装Chrome调试环境

### 1.1 创建Chrome调试目录
```cmd
mkdir C:\chrome-debug
cd C:\chrome-debug
```

### 1.2 创建Chrome调试启动脚本
创建文件 `start-chrome-debug.bat`:
```batch
@echo off
echo 正在启动Chrome调试模式...
echo 端口: 9222
echo 用户数据目录: %~dp0chrome-debug-data

REM 关闭现有Chrome进程
taskkill /f /im chrome.exe 2>nul

REM 等待进程完全关闭
timeout /t 2 /nobreak >nul

REM 启动Chrome调试模式
"C:\Program Files\Google\Chrome\Application\chrome.exe" ^
  --remote-debugging-port=9222 ^
  --user-data-dir="%~dp0chrome-debug-data" ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --no-first-run ^
  --no-default-browser-check ^
  http://************:3000

echo Chrome调试模式已启动
echo 调试端口: http://localhost:9222
echo MES系统: http://************:3000
pause
```

### 1.3 创建Chrome调试验证脚本
创建文件 `test-chrome-debug.bat`:
```batch
@echo off
echo 测试Chrome调试连接...
curl -s http://localhost:9222/json > debug-info.json
if %errorlevel% == 0 (
    echo ✓ Chrome调试端口连接成功
    echo 调试信息已保存到 debug-info.json
    type debug-info.json
) else (
    echo ✗ Chrome调试端口连接失败
    echo 请确保Chrome调试模式已启动
)
pause
```

## 第二步：安装Node.js和MCP工具

### 2.1 安装Node.js
1. 下载并安装 Node.js 18+ 从 https://nodejs.org
2. 验证安装：
```cmd
node --version
npm --version
```

### 2.2 安装MCP浏览器服务器
```cmd
cd C:\chrome-debug
npm init -y
npm install @modelcontextprotocol/server-browser
npm install axios
```

### 2.3 创建MCP测试脚本
创建文件 `test-mcp-connection.js`:
```javascript
const http = require('http');
const { spawn } = require('child_process');

console.log('=== MES系统 + Chrome MCP 调试测试 ===\n');

// 测试远程MES系统连接
async function testMESSystem() {
    console.log('1. 测试远程MES系统连接...');
    
    try {
        const response = await fetch('http://************:3000/api/health');
        const data = await response.json();
        console.log('✓ MES后端连接成功');
        console.log('  响应:', data.message);
        return true;
    } catch (error) {
        console.log('✗ MES后端连接失败:', error.message);
        return false;
    }
}

// 测试Chrome调试端口
function testChromeDebug() {
    return new Promise((resolve, reject) => {
        console.log('2. 测试Chrome调试端口...');
        
        const req = http.get('http://localhost:9222/json', (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const tabs = JSON.parse(data);
                    console.log(`✓ Chrome调试连接成功，发现 ${tabs.length} 个标签页`);
                    
                    // 查找MES系统标签页
                    const mesTab = tabs.find(tab => 
                        tab.url && tab.url.includes('************:3000')
                    );
                    
                    if (mesTab) {
                        console.log('✓ 找到MES系统标签页:', mesTab.title);
                    } else {
                        console.log('! 未找到MES系统标签页，请手动打开 http://************:3000');
                    }
                    
                    resolve(tabs);
                } catch (error) {
                    reject(new Error('解析调试信息失败: ' + error.message));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error('Chrome调试端口连接失败: ' + error.message));
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('连接超时'));
        });
    });
}

// 测试MCP服务器
function testMCPServer() {
    return new Promise((resolve, reject) => {
        console.log('3. 测试MCP服务器...');
        
        const mcpServer = spawn('node', [
            './node_modules/@modelcontextprotocol/server-browser/dist/index.js'
        ], {
            env: {
                ...process.env,
                BROWSER_TYPE: 'chromium',
                BROWSER_WS_ENDPOINT: 'ws://localhost:9222'
            }
        });
        
        let output = '';
        mcpServer.stdout.on('data', (data) => {
            output += data.toString();
            console.log('MCP输出:', data.toString().trim());
        });
        
        mcpServer.stderr.on('data', (data) => {
            console.log('MCP错误:', data.toString().trim());
        });
        
        // 5秒后检查状态
        setTimeout(() => {
            if (!mcpServer.killed) {
                console.log('✓ MCP服务器启动成功');
                mcpServer.kill();
                resolve(true);
            } else {
                reject(new Error('MCP服务器启动失败'));
            }
        }, 5000);
        
        mcpServer.on('error', (error) => {
            reject(new Error('MCP服务器错误: ' + error.message));
        });
    });
}

// 主测试流程
async function runTests() {
    try {
        // 测试MES系统
        const mesOk = await testMESSystem();
        
        // 测试Chrome调试
        await testChromeDebug();
        
        // 测试MCP服务器
        await testMCPServer();
        
        console.log('\n=== 测试完成 ===');
        console.log('✓ 所有测试通过');
        console.log('\n下一步操作:');
        console.log('1. 在Chrome中访问: http://************:3000');
        console.log('2. 在Chrome中访问: http://************:3001 (React前端)');
        console.log('3. 使用MCP工具与页面交互');
        console.log('4. 调试端口: http://localhost:9222');
        
    } catch (error) {
        console.error('\n✗ 测试失败:', error.message);
        console.log('\n故障排除:');
        console.log('1. 确保远程MES系统正在运行 (************:3000)');
        console.log('2. 确保Chrome以调试模式启动');
        console.log('3. 检查网络连接到远程服务器');
        console.log('4. 验证端口9222未被占用');
    }
}

// 运行测试
runTests();
```

## 第三步：启动调试环境

### 3.1 启动Chrome调试模式
```cmd
cd C:\chrome-debug
start-chrome-debug.bat
```

### 3.2 验证Chrome调试连接
```cmd
test-chrome-debug.bat
```

### 3.3 测试完整MCP连接
```cmd
node test-mcp-connection.js
```

## 第四步：访问远程MES系统

### 4.1 在Chrome中访问
- 后端API: http://************:3000
- 前端界面: http://************:3001
- 健康检查: http://************:3000/api/health

### 4.2 验证系统功能
1. 测试登录 (admin/admin123)
2. 查看项目列表
3. 查看工单列表
4. 测试API响应

## 第五步：MCP调试命令

### 5.1 基本MCP命令示例
```javascript
// 导航到MES系统
browser.navigate("http://************:3000");

// 点击登录按钮
browser.click("button[onclick='testLogin()']");

// 截图
browser.screenshot();

// 获取页面内容
browser.getContent();
```

## 故障排除

### 问题1：无法连接远程服务器
```cmd
# 测试网络连接
ping ************
telnet ************ 3000
```

### 问题2：Chrome调试端口被占用
```cmd
netstat -ano | findstr :9222
taskkill /PID <进程ID> /F
```

### 问题3：MCP连接失败
- 重新安装MCP包
- 检查Node.js版本
- 验证Chrome调试模式

## 成功标志
✓ Chrome调试端口 http://localhost:9222 可访问
✓ 远程MES后端 http://************:3000 可访问  
✓ 远程React前端 http://************:3001 可访问
✓ MCP服务器成功启动
✓ 可以在Chrome中正常访问MES系统
