# MES系统 MCP调试使用指南

## 🎯 当前状态

### ✅ 远程服务器 (************) - 已启动
- **MES后端**: http://************:3000 ✅ 运行正常
- **React前端**: http://************:3001 ✅ 运行正常
- **测试账号**: admin / admin123

### ⚠️ 本地Windows机器 - 需要配置
- **Chrome调试**: localhost:9222 ⚠️ 需要启动

## 🚀 开始调试步骤

### 第一步：启动本地Chrome调试模式

1. **运行Chrome调试启动脚本**：
   ```cmd
   # 方式1: 双击桌面快捷方式
   双击 "MES Chrome调试.lnk"
   
   # 方式2: 运行批处理文件
   C:\chrome-debug-mes\start-chrome-debug.bat
   
   # 方式3: 手动启动
   "C:\Program Files\Google\Chrome\Application\chrome.exe" ^
     --remote-debugging-port=9222 ^
     --user-data-dir="C:\chrome-debug-mes\chrome-debug-data" ^
     --disable-web-security ^
     http://************:3000
   ```

2. **验证Chrome调试端口**：
   ```cmd
   # 在本地Windows机器上运行
   curl http://localhost:9222/json
   
   # 或在浏览器中访问
   http://localhost:9222
   ```

### 第二步：访问MES系统

在启动的Chrome调试浏览器中访问：

1. **MES后端主页**: http://************:3000
   - 点击 "健康检查" 按钮测试API
   - 点击 "登录测试" 按钮测试认证
   - 点击 "项目列表" 按钮测试数据
   - 点击 "工单列表" 按钮测试功能

2. **React前端界面**: http://************:3001
   - 浏览MES系统界面
   - 测试各个功能模块
   - 验证前后端通信

### 第三步：运行MCP测试

1. **在本地Windows机器上运行测试脚本**：
   ```cmd
   cd C:\chrome-debug-mes
   node local-mcp-test.js
   ```

2. **查看测试结果**：
   - Chrome调试端口状态
   - 远程MES系统连接状态
   - 可用的MCP调试命令

## 🔧 MCP调试命令

### 基本页面操作

```javascript
// 导航到MES后端
await browser.navigate("http://************:3000");

// 导航到React前端
await browser.navigate("http://************:3001");

// 截图
await browser.screenshot();

// 获取页面内容
const content = await browser.getContent();
```

### 页面交互

```javascript
// 点击健康检查按钮
await browser.click("button[onclick='testHealth()']");

// 点击登录测试按钮
await browser.click("button[onclick='testLogin()']");

// 点击项目列表按钮
await browser.click("button[onclick='testProjects()']");

// 点击工单列表按钮
await browser.click("button[onclick='testWorkOrders()']");
```

### 表单操作

```javascript
// 输入用户名
await browser.type("#username", "admin");

// 输入密码
await browser.type("#password", "admin123");

// 提交表单
await browser.click("button[type='submit']");
```

### 高级调试

```javascript
// 等待元素出现
await browser.waitForElement("#results");

// 获取元素文本
const text = await browser.getText(".result");

// 执行JavaScript
const title = await browser.evaluate("document.title");

// 测试API
await browser.evaluate(`
  fetch('/api/health')
    .then(r => r.json())
    .then(data => console.log('Health:', data))
`);
```

## 🧪 快速测试脚本

复制以下代码到MCP控制台中执行：

```javascript
async function quickMESTest() {
  console.log('🚀 开始MES系统快速测试...');
  
  try {
    // 1. 导航到MES后端
    console.log('1. 导航到MES后端...');
    await browser.navigate("http://************:3000");
    await browser.waitForElement("body");
    
    // 2. 截图
    console.log('2. 截图...');
    await browser.screenshot();
    
    // 3. 测试健康检查
    console.log('3. 测试健康检查...');
    await browser.click("button[onclick='testHealth()']");
    await browser.waitForElement("#results");
    
    // 4. 测试登录
    console.log('4. 测试登录...');
    await browser.click("button[onclick='testLogin()']");
    
    // 5. 导航到React前端
    console.log('5. 导航到React前端...');
    await browser.navigate("http://************:3001");
    await browser.waitForElement("body");
    
    // 6. 最终截图
    console.log('6. 最终截图...');
    await browser.screenshot();
    
    console.log('✅ 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
quickMESTest();
```

## 🔍 故障排除

### 问题1: Chrome调试端口连接失败
```cmd
# 检查端口占用
netstat -ano | findstr :9222

# 终止占用进程
taskkill /PID <进程ID> /F

# 重新启动Chrome调试
C:\chrome-debug-mes\start-chrome-debug.bat
```

### 问题2: 无法访问远程MES系统
```cmd
# 测试网络连接
ping ************

# 测试端口连通性
telnet ************ 3000
telnet ************ 3001
```

### 问题3: MCP命令执行失败
1. 确保Chrome调试模式正常运行
2. 确保已在Chrome中打开MES系统页面
3. 检查MCP服务器连接状态
4. 验证命令语法正确性

## 📊 系统监控

### 实时状态检查
```javascript
// 检查系统状态
await browser.evaluate(`
  Promise.all([
    fetch('/api/health').then(r => r.json()),
    fetch('/api/projects').then(r => r.json()),
    fetch('/api/work-orders').then(r => r.json())
  ]).then(results => {
    console.log('系统状态:', results);
  });
`);
```

### 性能监控
```javascript
// 页面加载性能
await browser.evaluate(`
  console.log('页面性能:', {
    loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
    domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
    firstPaint: performance.getEntriesByType('paint')[0]?.startTime
  });
`);
```

## 🎉 成功标志

当您看到以下所有项目都正常时，说明MCP调试环境配置成功：

- ✅ Chrome调试端口 http://localhost:9222 可访问
- ✅ 远程MES后端 http://************:3000 可访问
- ✅ 远程React前端 http://************:3001 可访问
- ✅ MCP命令可以正常执行
- ✅ 可以与MES系统进行交互
- ✅ 登录测试成功 (admin/admin123)

## 📞 获取帮助

如果遇到问题，请提供：
1. 错误信息截图
2. Chrome调试页面状态 (http://localhost:9222)
3. 网络连接测试结果
4. MCP命令执行日志

祝您调试愉快！🎯
