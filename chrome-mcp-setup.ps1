# Chrome MCP环境配置脚本

Write-Host "=== Chrome MCP环境配置 ===" -ForegroundColor Green

# 创建MCP工作目录
$mcpDir = "$env:USERPROFILE\chrome-mcp"
Write-Host "创建MCP目录: $mcpDir" -ForegroundColor Yellow
if (!(Test-Path $mcpDir)) {
    New-Item -ItemType Directory -Path $mcpDir -Force
}
Set-Location $mcpDir

# 检查Node.js
Write-Host "检查Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 初始化npm项目
if (!(Test-Path "package.json")) {
    Write-Host "初始化npm项目..." -ForegroundColor Yellow
    npm init -y
}

# 安装MCP相关包
Write-Host "安装MCP和浏览器自动化包..." -ForegroundColor Yellow
$packages = @(
    "@modelcontextprotocol/server-browser",
    "puppeteer",
    "playwright",
    "chrome-launcher",
    "ws"
)

foreach ($package in $packages) {
    Write-Host "安装 $package..." -ForegroundColor White
    npm install $package
}

# 创建MCP配置文件
$mcpConfig = @{
    mcpServers = @{
        browser = @{
            command = "node"
            args = @("$mcpDir\chrome-mcp-server.js")
            env = @{
                BROWSER_TYPE = "chrome"
                BROWSER_WS_ENDPOINT = "ws://localhost:9222"
                MES_SERVER_URL = "http://************:3000"
            }
        }
    }
} | ConvertTo-Json -Depth 3

$mcpConfig | Out-File -FilePath "$mcpDir\mcp-config.json" -Encoding UTF8
Write-Host "✓ MCP配置文件已创建" -ForegroundColor Green

# 创建自定义MCP服务器
$mcpServerCode = @"
// Chrome MCP服务器
const puppeteer = require('puppeteer');
const WebSocket = require('ws');

class ChromeMCPServer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.mesUrl = process.env.MES_SERVER_URL || 'http://************:3000';
    }

    async connect() {
        try {
            console.log('连接到Chrome调试端口...');
            this.browser = await puppeteer.connect({
                browserWSEndpoint: 'ws://localhost:9222'
            });
            
            const pages = await this.browser.pages();
            this.page = pages.find(p => p.url().includes('************')) || pages[0];
            
            if (!this.page) {
                this.page = await this.browser.newPage();
                await this.page.goto(this.mesUrl);
            }
            
            console.log('✓ 已连接到Chrome');
            return true;
        } catch (error) {
            console.error('连接失败:', error.message);
            return false;
        }
    }

    async navigate(url) {
        if (!this.page) await this.connect();
        await this.page.goto(url);
        return { success: true, url: await this.page.url() };
    }

    async click(selector) {
        if (!this.page) await this.connect();
        await this.page.click(selector);
        return { success: true, selector };
    }

    async type(selector, text) {
        if (!this.page) await this.connect();
        await this.page.type(selector, text);
        return { success: true, selector, text };
    }

    async screenshot(filename = 'screenshot.png') {
        if (!this.page) await this.connect();
        await this.page.screenshot({ path: filename });
        return { success: true, filename };
    }

    async evaluate(code) {
        if (!this.page) await this.connect();
        const result = await this.page.evaluate(code);
        return { success: true, result };
    }

    async getPageInfo() {
        if (!this.page) await this.connect();
        return {
            title: await this.page.title(),
            url: await this.page.url(),
            viewport: await this.page.viewport()
        };
    }

    async testMESSystem() {
        console.log('开始MES系统测试...');
        const results = [];
        
        try {
            // 导航到MES系统
            await this.navigate(this.mesUrl);
            results.push({ test: '页面加载', status: 'pass' });
            
            // 截图
            await this.screenshot('mes-homepage.png');
            results.push({ test: '首页截图', status: 'pass' });
            
            // 检查页面元素
            const hasMenu = await this.page.$('.ant-menu') !== null;
            results.push({ test: '导航菜单', status: hasMenu ? 'pass' : 'fail' });
            
            const hasContent = await this.page.$('.ant-layout-content') !== null;
            results.push({ test: '主要内容', status: hasContent ? 'pass' : 'fail' });
            
            console.log('MES系统测试完成');
            return { success: true, results };
        } catch (error) {
            console.error('MES测试失败:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 启动服务器
const server = new ChromeMCPServer();

// 处理命令行参数
const command = process.argv[2];
const args = process.argv.slice(3);

async function main() {
    switch (command) {
        case 'connect':
            await server.connect();
            break;
        case 'navigate':
            await server.navigate(args[0]);
            break;
        case 'test':
            await server.testMESSystem();
            break;
        case 'screenshot':
            await server.screenshot(args[0]);
            break;
        default:
            console.log('可用命令:');
            console.log('  connect    - 连接到Chrome');
            console.log('  navigate   - 导航到URL');
            console.log('  test       - 测试MES系统');
            console.log('  screenshot - 截图');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ChromeMCPServer;
"@

$mcpServerCode | Out-File -FilePath "$mcpDir\chrome-mcp-server.js" -Encoding UTF8
Write-Host "✓ Chrome MCP服务器已创建" -ForegroundColor Green

# 创建测试脚本
$testScript = @"
// Chrome MCP测试脚本
const ChromeMCPServer = require('./chrome-mcp-server');

async function runTests() {
    console.log('=== Chrome MCP测试 ===');
    
    const server = new ChromeMCPServer();
    
    try {
        // 连接测试
        console.log('1. 测试Chrome连接...');
        const connected = await server.connect();
        if (!connected) {
            console.log('✗ Chrome连接失败');
            return;
        }
        console.log('✓ Chrome连接成功');
        
        // 页面信息
        console.log('2. 获取页面信息...');
        const pageInfo = await server.getPageInfo();
        console.log('✓ 页面信息:', pageInfo);
        
        // MES系统测试
        console.log('3. 测试MES系统...');
        const mesTest = await server.testMESSystem();
        console.log('✓ MES测试结果:', mesTest);
        
        console.log('\\n=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

runTests();
"@

$testScript | Out-File -FilePath "$mcpDir\test-chrome-mcp.js" -Encoding UTF8
Write-Host "✓ 测试脚本已创建" -ForegroundColor Green

Write-Host "`n=== Chrome MCP环境配置完成 ===" -ForegroundColor Green
Write-Host "目录: $mcpDir" -ForegroundColor Cyan
Write-Host "`n下一步:" -ForegroundColor Yellow
Write-Host "1. 启动Chrome调试模式" -ForegroundColor White
Write-Host "2. 运行: node test-chrome-mcp.js" -ForegroundColor White
Write-Host "3. 使用MCP工具与Chrome交互" -ForegroundColor White
