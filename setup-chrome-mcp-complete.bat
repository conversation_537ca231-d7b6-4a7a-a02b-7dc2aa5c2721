@echo off
title Chrome MCP 一键安装
color 0A

echo ========================================
echo    Chrome MCP 调试环境一键安装
echo ========================================
echo.

echo [1/4] 安装Chrome调试环境...
powershell -ExecutionPolicy Bypass -File "install-chrome-debug.ps1"
if %errorlevel% neq 0 (
    echo 错误：Chrome安装失败
    pause
    exit /b 1
)

echo.
echo [2/4] 配置MCP环境...
powershell -ExecutionPolicy Bypass -File "chrome-mcp-setup.ps1"
if %errorlevel% neq 0 (
    echo 错误：MCP配置失败
    pause
    exit /b 1
)

echo.
echo [3/4] 启动Chrome调试模式...
start "Chrome调试" "%USERPROFILE%\chrome-debug\start-chrome-debug.bat"

echo 等待Chrome启动...
timeout /t 10 >nul

echo.
echo [4/4] 测试MCP连接...
cd "%USERPROFILE%\chrome-mcp"
node test-chrome-mcp.js

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. Chrome调试模式已启动
echo 2. 访问 http://localhost:9222 查看调试信息
echo 3. MES系统: http://************:3000
echo 4. 运行测试: cd %USERPROFILE%\chrome-mcp ^&^& node test-chrome-mcp.js
echo.
echo 桌面快捷方式: Chrome调试模式.lnk
echo.
pause
