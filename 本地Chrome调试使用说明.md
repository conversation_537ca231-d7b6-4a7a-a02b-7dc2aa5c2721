# 本地Chrome MCP调试使用说明

## 🎯 目标
在您的本地Windows机器上配置Chrome调试环境，连接到远程Ubuntu服务器(************)上的MES系统进行调试。

## 📋 前提条件
- ✅ Windows 10/11
- ✅ 管理员权限
- ✅ 网络可访问远程服务器 ************
- ✅ 远程MES系统正在运行

## 🚀 快速开始

### 步骤1：下载配置文件
将以下文件下载到您的本地Windows机器：
- `setup-local-chrome-mcp.ps1` (安装脚本)
- `LOCAL_CHROME_MCP_SETUP.md` (详细说明)

### 步骤2：运行安装脚本
1. **右键点击PowerShell，选择"以管理员身份运行"**
2. **导航到下载目录并运行安装脚本：**
```powershell
cd C:\Downloads  # 或您的下载目录
.\setup-local-chrome-mcp.ps1
```

### 步骤3：启动Chrome调试
安装完成后，您可以通过以下方式启动：
- **方式1：** 双击桌面快捷方式 "MES Chrome调试"
- **方式2：** 运行 `C:\chrome-debug-mes\start-chrome-debug.bat`

### 步骤4：验证连接
```cmd
cd C:\chrome-debug-mes
test-connections.bat
node test-mcp-connection.js
```

## 🌐 访问地址

### 远程MES系统
- **后端API：** http://************:3000
- **React前端：** http://************:3001
- **健康检查：** http://************:3000/api/health

### 本地调试
- **Chrome调试端口：** http://localhost:9222
- **调试信息页面：** http://localhost:9222/json

## 🧪 测试功能

### 1. 测试MES系统登录
- 用户名：`admin`
- 密码：`admin123`
- 登录地址：http://************:3000

### 2. 测试API接口
在Chrome中访问以下地址：
- http://************:3000/api/health (健康检查)
- http://************:3000/api/projects (项目列表)
- http://************:3000/api/work-orders (工单列表)

### 3. 测试React前端
访问：http://************:3001
- 查看MES系统界面
- 测试各个功能模块
- 验证前后端通信

## 🔧 MCP调试命令

### 基本浏览器操作
```javascript
// 导航到MES系统
browser.navigate("http://************:3000");

// 点击健康检查按钮
browser.click("button[onclick='testHealth()']");

// 点击登录测试按钮  
browser.click("button[onclick='testLogin()']");

// 截图
browser.screenshot();

// 获取页面内容
browser.getContent();
```

### 高级调试操作
```javascript
// 等待元素出现
browser.waitForElement("#results");

// 输入文本
browser.type("#username", "admin");

// 获取元素文本
browser.getText(".result");

// 执行JavaScript
browser.evaluate("document.title");
```

## 📊 系统状态检查

### 检查远程服务器状态
```cmd
# 测试网络连接
ping ************

# 测试端口连通性
telnet ************ 3000
telnet ************ 3001
```

### 检查本地Chrome调试
```cmd
# 检查调试端口
netstat -ano | findstr :9222

# 测试调试接口
curl http://localhost:9222/json
```

## ❗ 故障排除

### 问题1：无法连接远程服务器
**症状：** 无法访问 http://************:3000
**解决方案：**
1. 检查网络连接：`ping ************`
2. 确认远程MES系统正在运行
3. 检查防火墙设置
4. 验证端口转发配置

### 问题2：Chrome调试端口被占用
**症状：** 端口9222被占用
**解决方案：**
```cmd
# 查找占用进程
netstat -ano | findstr :9222

# 终止进程
taskkill /PID <进程ID> /F

# 重新启动Chrome调试
start-chrome-debug.bat
```

### 问题3：MCP连接失败
**症状：** MCP服务器无法启动
**解决方案：**
1. 检查Node.js版本：`node --version`
2. 重新安装MCP包：`npm install @modelcontextprotocol/server-browser --force`
3. 确保Chrome调试模式已启动
4. 检查调试端口是否可访问

### 问题4：Chrome无法启动调试模式
**症状：** Chrome启动失败或无调试功能
**解决方案：**
1. 完全关闭所有Chrome进程
2. 检查Chrome安装路径
3. 手动运行调试命令：
```cmd
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222
```

## 📞 获取帮助

如果遇到问题，请提供以下信息：
1. 错误信息截图
2. PowerShell输出日志
3. Chrome调试页面状态
4. 网络连接测试结果
5. 系统环境信息

## 🎉 成功标志

当看到以下所有项目都显示 ✅ 时，说明配置成功：
- ✅ Chrome调试端口 http://localhost:9222 可访问
- ✅ 远程MES后端 http://************:3000 可访问
- ✅ 远程React前端 http://************:3001 可访问
- ✅ MCP服务器成功启动
- ✅ 可以在Chrome中正常访问MES系统
- ✅ 登录测试成功 (admin/admin123)

## 📝 下一步

配置成功后，您可以：
1. 使用MCP工具自动化测试MES系统
2. 创建自定义调试脚本
3. 集成到CI/CD流程中
4. 开发更多测试用例
