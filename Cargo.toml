[package]
name = "mes-system-simple"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "mes-simple"
path = "src/main_simple.rs"

[lib]
name = "mes_system_simple"
path = "src/lib_simple.rs"

[dependencies]
# Web framework
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["macros"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "postgres", "migrate", "macros", "chrono", "bigdecimal"] }
validator = { version = "0.20.0", features = ["derive"] }
bcrypt = "0.17.0"
jsonwebtoken = "9.3.1"
base64 = "0.22.1"
thiserror = "2.0.12"
bigdecimal = { version = "0.4.8", features = ["serde"] }
rust_decimal = "1.37.2"
