#!/usr/bin/env node

/**
 * 本地MCP测试脚本
 * 在您的本地Windows机器上运行此脚本
 * 用于测试MCP与远程MES系统的交互
 */

const http = require('http');

// 配置
const CONFIG = {
  SERVER_IP: '************',
  BACKEND_PORT: 3000,
  FRONTEND_PORT: 3001,
  DEBUG_PORT: 9222
};

console.log('🔧 本地MCP调试测试');
console.log('='.repeat(50));
console.log(`远程MES服务器: ${CONFIG.SERVER_IP}`);
console.log(`本地Chrome调试: localhost:${CONFIG.DEBUG_PORT}`);
console.log('='.repeat(50));

// 测试本地Chrome调试端口
function testLocalChromeDebug() {
  return new Promise((resolve, reject) => {
    console.log('\n🔍 1. 测试本地Chrome调试端口...');
    
    const req = http.get(`http://localhost:${CONFIG.DEBUG_PORT}/json`, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const tabs = JSON.parse(data);
          console.log(`✅ Chrome调试连接成功`);
          console.log(`   发现标签页: ${tabs.length} 个`);
          
          // 查找MES系统相关标签页
          const mesTabs = tabs.filter(tab => 
            tab.url && (
              tab.url.includes(`${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}`) ||
              tab.url.includes(`${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`)
            )
          );
          
          if (mesTabs.length > 0) {
            console.log(`✅ 找到MES系统标签页: ${mesTabs.length} 个`);
            mesTabs.forEach((tab, index) => {
              console.log(`   ${index + 1}. ${tab.title || 'Untitled'}`);
              console.log(`      URL: ${tab.url}`);
              console.log(`      ID: ${tab.id}`);
              console.log(`      WebSocket: ${tab.webSocketDebuggerUrl ? '✅ 可用' : '❌ 不可用'}`);
            });
          } else {
            console.log('⚠️  未找到MES系统标签页');
            console.log('   请在Chrome中打开以下地址:');
            console.log(`   - http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}`);
            console.log(`   - http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`);
          }
          
          resolve({ tabs, mesTabs });
        } catch (error) {
          reject(new Error(`解析调试信息失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`Chrome调试端口连接失败: ${error.message}`));
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('连接超时 - 请确保Chrome调试模式已启动'));
    });
  });
}

// 测试远程MES系统连接
async function testRemoteMESConnection() {
  console.log('\n🔍 2. 测试远程MES系统连接...');
  
  try {
    // 测试后端
    console.log('   测试后端API...');
    const backendResponse = await fetch(`http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}/api/health`);
    if (backendResponse.ok) {
      const data = await backendResponse.json();
      console.log(`   ✅ 后端连接成功: ${data.message}`);
    } else {
      console.log(`   ❌ 后端连接失败: ${backendResponse.status}`);
    }
    
    // 测试前端
    console.log('   测试前端页面...');
    const frontendResponse = await fetch(`http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`, {
      method: 'HEAD'
    });
    if (frontendResponse.ok) {
      console.log(`   ✅ 前端连接成功: ${frontendResponse.status}`);
    } else {
      console.log(`   ❌ 前端连接失败: ${frontendResponse.status}`);
    }
    
    return true;
  } catch (error) {
    console.log(`   ❌ 远程连接失败: ${error.message}`);
    return false;
  }
}

// 生成MCP调试命令
function generateMCPCommands() {
  console.log('\n📋 3. MCP调试命令示例');
  console.log('-'.repeat(40));
  
  const commands = [
    {
      category: '🌐 页面导航',
      commands: [
        `// 导航到MES后端主页`,
        `await browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}");`,
        ``,
        `// 导航到React前端`,
        `await browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}");`
      ]
    },
    {
      category: '🔘 页面交互',
      commands: [
        `// 点击健康检查按钮`,
        `await browser.click("button[onclick='testHealth()']");`,
        ``,
        `// 点击登录测试按钮`,
        `await browser.click("button[onclick='testLogin()']");`,
        ``,
        `// 点击项目列表按钮`,
        `await browser.click("button[onclick='testProjects()']");`,
        ``,
        `// 点击工单列表按钮`,
        `await browser.click("button[onclick='testWorkOrders()']");`
      ]
    },
    {
      category: '📝 表单操作',
      commands: [
        `// 输入用户名`,
        `await browser.type("#username", "admin");`,
        ``,
        `// 输入密码`,
        `await browser.type("#password", "admin123");`,
        ``,
        `// 提交表单`,
        `await browser.click("button[type='submit']");`
      ]
    },
    {
      category: '🔍 页面检查',
      commands: [
        `// 截图`,
        `await browser.screenshot();`,
        ``,
        `// 获取页面内容`,
        `const content = await browser.getContent();`,
        ``,
        `// 等待元素出现`,
        `await browser.waitForElement("#results");`,
        ``,
        `// 获取元素文本`,
        `const text = await browser.getText(".result");`,
        ``,
        `// 执行JavaScript`,
        `const title = await browser.evaluate("document.title");`
      ]
    },
    {
      category: '🧪 API测试',
      commands: [
        `// 测试健康检查API`,
        `await browser.evaluate(\``,
        `  fetch('/api/health')`,
        `    .then(r => r.json())`,
        `    .then(data => console.log('Health:', data))`,
        `\`);`,
        ``,
        `// 测试登录API`,
        `await browser.evaluate(\``,
        `  fetch('/api/auth/login', {`,
        `    method: 'POST',`,
        `    headers: { 'Content-Type': 'application/json' },`,
        `    body: JSON.stringify({ username: 'admin', password: 'admin123' })`,
        `  })`,
        `  .then(r => r.json())`,
        `  .then(data => console.log('Login:', data))`,
        `\`);`
      ]
    }
  ];
  
  commands.forEach(section => {
    console.log(`\n${section.category}:`);
    section.commands.forEach(cmd => {
      console.log(`   ${cmd}`);
    });
  });
}

// 生成快速测试脚本
function generateQuickTestScript() {
  console.log('\n🚀 4. 快速测试脚本');
  console.log('-'.repeat(40));
  
  const script = `
// 复制以下代码到MCP控制台中执行

async function quickMESTest() {
  console.log('🚀 开始MES系统快速测试...');
  
  try {
    // 1. 导航到MES后端
    console.log('1. 导航到MES后端...');
    await browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}");
    await browser.waitForElement("body");
    
    // 2. 截图
    console.log('2. 截图...');
    await browser.screenshot();
    
    // 3. 测试健康检查
    console.log('3. 测试健康检查...');
    await browser.click("button[onclick='testHealth()']");
    await browser.waitForElement("#results");
    
    // 4. 测试登录
    console.log('4. 测试登录...');
    await browser.click("button[onclick='testLogin()']");
    
    // 5. 导航到React前端
    console.log('5. 导航到React前端...');
    await browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}");
    await browser.waitForElement("body");
    
    // 6. 最终截图
    console.log('6. 最终截图...');
    await browser.screenshot();
    
    console.log('✅ 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
quickMESTest();
`;
  
  console.log(script);
}

// 主测试流程
async function runTests() {
  try {
    console.log('🚀 开始本地MCP调试测试...\n');
    
    // 测试本地Chrome调试
    const debugResult = await testLocalChromeDebug();
    
    // 测试远程MES连接
    await testRemoteMESConnection();
    
    // 生成调试命令
    generateMCPCommands();
    
    // 生成快速测试脚本
    generateQuickTestScript();
    
    console.log('\n🎉 本地MCP调试环境就绪！');
    console.log('='.repeat(50));
    console.log('✅ Chrome调试端口: 正常');
    console.log('✅ 远程MES系统: 可访问');
    console.log(`✅ 发现标签页: ${debugResult.tabs.length} 个`);
    console.log(`✅ MES标签页: ${debugResult.mesTabs.length} 个`);
    
    console.log('\n📱 下一步操作:');
    console.log('1. 在Chrome中访问MES系统');
    console.log('2. 使用上述MCP命令进行调试');
    console.log('3. 运行快速测试脚本验证功能');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.log('\n🔧 故障排除:');
    console.log('1. 确保Chrome调试模式已启动 (端口9222)');
    console.log('2. 运行: C:\\chrome-debug-mes\\start-chrome-debug.bat');
    console.log('3. 检查Chrome是否以调试模式运行');
    console.log('4. 验证网络连接到远程服务器');
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  testLocalChromeDebug,
  testRemoteMESConnection,
  CONFIG
};
