use axum::{
    response::J<PERSON>,
    Extension,
    extract::State,
};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::role::Role,
    require_any_role,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemHealth {
    pub status: String,
    pub timestamp: DateTime<Utc>,
    pub database: DatabaseHealth,
    pub performance: PerformanceMetrics,
    pub resources: ResourceUsage,
    pub version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseHealth {
    pub status: String,
    pub connection_count: i32,
    pub active_connections: i32,
    pub response_time_ms: f64,
    pub total_queries: i64,
    pub slow_queries: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub avg_response_time_ms: f64,
    pub requests_per_minute: f64,
    pub error_rate: f64,
    pub cache_hit_rate: f64,
    pub throughput: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub disk_usage_percent: f64,
    pub network_io_mb: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemOptimization {
    pub recommendations: Vec<OptimizationRecommendation>,
    pub performance_score: f64,
    pub bottlenecks: Vec<Bottleneck>,
    pub maintenance_tasks: Vec<MaintenanceTask>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OptimizationRecommendation {
    pub category: String,
    pub priority: String, // "HIGH", "MEDIUM", "LOW"
    pub title: String,
    pub description: String,
    pub impact: String,
    pub effort: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Bottleneck {
    pub component: String,
    pub severity: String,
    pub description: String,
    pub suggested_action: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MaintenanceTask {
    pub task_type: String,
    pub description: String,
    pub frequency: String,
    pub last_executed: Option<DateTime<Utc>>,
    pub next_due: DateTime<Utc>,
    pub priority: String,
}

pub async fn get_system_health(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<SystemHealth>> {
    // 只有管理员可以查看系统健康状态
    require_any_role!(current_user, Role::ADMIN);

    let start_time = std::time::Instant::now();
    
    // 检查数据库连接
    let db_test = sqlx::query_scalar!("SELECT 1 as test")
        .fetch_one(&database.pool)
        .await;
    
    let db_response_time = start_time.elapsed().as_millis() as f64;
    
    let database_health = match db_test {
        Ok(_) => {
            // 获取数据库统计信息
            let db_stats = sqlx::query!(
                r#"
                SELECT 
                    (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections
                "#
            )
            .fetch_optional(&database.pool)
            .await?;

            if let Some(db_stats) = db_stats {
                DatabaseHealth {
                    status: "HEALTHY".to_string(),
                    connection_count: db_stats.total_connections.unwrap_or(0) as i32,
                    active_connections: db_stats.active_connections.unwrap_or(0) as i32,
                    response_time_ms: db_response_time,
                    total_queries: 0, // 可以从pg_stat_statements获取
                    slow_queries: 0,
                }
            } else {
                DatabaseHealth {
                    status: "HEALTHY".to_string(),
                    connection_count: 0,
                    active_connections: 0,
                    response_time_ms: db_response_time,
                    total_queries: 0,
                    slow_queries: 0,
                }
            }
        }
        Err(_) => DatabaseHealth {
            status: "UNHEALTHY".to_string(),
            connection_count: 0,
            active_connections: 0,
            response_time_ms: db_response_time,
            total_queries: 0,
            slow_queries: 0,
        }
    };

    // 模拟性能指标（实际应用中应该从监控系统获取）
    let performance = PerformanceMetrics {
        avg_response_time_ms: db_response_time,
        requests_per_minute: 120.0,
        error_rate: 0.5,
        cache_hit_rate: 95.0,
        throughput: 1000.0,
    };

    // 模拟资源使用情况
    let resources = ResourceUsage {
        memory_usage_mb: 512.0,
        cpu_usage_percent: 25.0,
        disk_usage_percent: 45.0,
        network_io_mb: 10.5,
    };

    let overall_status = if database_health.status == "HEALTHY" && 
                           performance.avg_response_time_ms < 1000.0 &&
                           resources.cpu_usage_percent < 80.0 {
        "HEALTHY"
    } else if performance.avg_response_time_ms > 2000.0 || 
              resources.cpu_usage_percent > 90.0 {
        "CRITICAL"
    } else {
        "WARNING"
    };

    Ok(Json(SystemHealth {
        status: overall_status.to_string(),
        timestamp: Utc::now(),
        database: database_health,
        performance,
        resources,
        version: env!("CARGO_PKG_VERSION").to_string(),
    }))
}

pub async fn get_system_optimization(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<SystemOptimization>> {
    require_any_role!(current_user, Role::ADMIN);

    // 分析数据库性能
    let table_stats = sqlx::query!(
        r#"
        SELECT
            schemaname,
            relname as tablename,
            n_tup_ins + n_tup_upd + n_tup_del as total_operations,
            seq_scan,
            seq_tup_read,
            idx_scan,
            idx_tup_fetch
        FROM pg_stat_user_tables
        ORDER BY total_operations DESC
        LIMIT 10
        "#
    )
    .fetch_all(&database.pool)
    .await
    .unwrap_or_default();

    let mut recommendations = Vec::new();
    let mut bottlenecks = Vec::new();

    // 分析表扫描情况
    for table in &table_stats {
        let seq_scan = table.seq_scan.unwrap_or(0);
        let idx_scan = table.idx_scan.unwrap_or(0);
        
        if seq_scan > idx_scan && seq_scan > 1000 {
            recommendations.push(OptimizationRecommendation {
                category: "DATABASE".to_string(),
                priority: "HIGH".to_string(),
                title: format!("为表 {} 添加索引", table.tablename),
                description: format!("表 {} 的顺序扫描次数 ({}) 远超索引扫描 ({})", 
                                   table.tablename, seq_scan, idx_scan),
                impact: "显著提升查询性能".to_string(),
                effort: "中等".to_string(),
            });

            bottlenecks.push(Bottleneck {
                component: format!("表: {}", table.tablename),
                severity: "HIGH".to_string(),
                description: "频繁的全表扫描".to_string(),
                suggested_action: "添加适当的索引".to_string(),
            });
        }
    }

    // 检查执行日志数据量
    let log_count = sqlx::query_scalar!(
        "SELECT COUNT(*) FROM execution_logs WHERE event_time < CURRENT_DATE - INTERVAL '90 days'"
    )
    .fetch_one(&database.pool)
    .await
    .unwrap_or(0);

    if log_count.unwrap_or(0) > 10000 {
        recommendations.push(OptimizationRecommendation {
            category: "MAINTENANCE".to_string(),
            priority: "MEDIUM".to_string(),
            title: "清理历史执行日志".to_string(),
            description: format!("发现 {} 条90天前的执行日志记录", log_count.unwrap_or(0)),
            impact: "减少存储空间，提升查询性能".to_string(),
            effort: "低".to_string(),
        });
    }

    // 通用优化建议
    recommendations.push(OptimizationRecommendation {
        category: "PERFORMANCE".to_string(),
        priority: "MEDIUM".to_string(),
        title: "启用查询缓存".to_string(),
        description: "为频繁查询的报表数据启用缓存机制".to_string(),
        impact: "减少数据库负载，提升响应速度".to_string(),
        effort: "中等".to_string(),
    });

    recommendations.push(OptimizationRecommendation {
        category: "MONITORING".to_string(),
        priority: "LOW".to_string(),
        title: "设置性能监控告警".to_string(),
        description: "为关键性能指标设置自动告警".to_string(),
        impact: "及时发现性能问题".to_string(),
        effort: "低".to_string(),
    });

    // 维护任务
    let maintenance_tasks = vec![
        MaintenanceTask {
            task_type: "DATABASE_VACUUM".to_string(),
            description: "数据库清理和统计信息更新".to_string(),
            frequency: "每周".to_string(),
            last_executed: Some(Utc::now() - chrono::Duration::days(3)),
            next_due: Utc::now() + chrono::Duration::days(4),
            priority: "HIGH".to_string(),
        },
        MaintenanceTask {
            task_type: "LOG_CLEANUP".to_string(),
            description: "清理90天前的执行日志".to_string(),
            frequency: "每月".to_string(),
            last_executed: Some(Utc::now() - chrono::Duration::days(25)),
            next_due: Utc::now() + chrono::Duration::days(5),
            priority: "MEDIUM".to_string(),
        },
        MaintenanceTask {
            task_type: "BACKUP_VERIFICATION".to_string(),
            description: "验证数据库备份完整性".to_string(),
            frequency: "每天".to_string(),
            last_executed: Some(Utc::now() - chrono::Duration::hours(20)),
            next_due: Utc::now() + chrono::Duration::hours(4),
            priority: "HIGH".to_string(),
        },
    ];

    // 计算性能评分
    let performance_score = calculate_performance_score(&recommendations, &bottlenecks);

    Ok(Json(SystemOptimization {
        recommendations,
        performance_score,
        bottlenecks,
        maintenance_tasks,
    }))
}

fn calculate_performance_score(
    recommendations: &[OptimizationRecommendation],
    bottlenecks: &[Bottleneck],
) -> f64 {
    let mut score = 100.0;
    
    // 根据建议数量和优先级扣分
    for rec in recommendations {
        match rec.priority.as_str() {
            "HIGH" => score -= 15.0,
            "MEDIUM" => score -= 8.0,
            "LOW" => score -= 3.0,
            _ => {}
        }
    }
    
    // 根据瓶颈扣分
    for bottleneck in bottlenecks {
        match bottleneck.severity.as_str() {
            "HIGH" => score -= 20.0,
            "MEDIUM" => score -= 10.0,
            "LOW" => score -= 5.0,
            _ => {}
        }
    }
    
    score.max(0.0).min(100.0)
}

pub async fn get_system_metrics(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<serde_json::Value>> {
    require_any_role!(current_user, Role::ADMIN);

    let metrics = sqlx::query!(
        r#"
        SELECT 
            'users' as table_name,
            COUNT(*) as record_count,
            pg_size_pretty(pg_total_relation_size('users')) as table_size
        FROM users
        UNION ALL
        SELECT 
            'plan_tasks' as table_name,
            COUNT(*) as record_count,
            pg_size_pretty(pg_total_relation_size('plan_tasks')) as table_size
        FROM plan_tasks
        UNION ALL
        SELECT 
            'execution_logs' as table_name,
            COUNT(*) as record_count,
            pg_size_pretty(pg_total_relation_size('execution_logs')) as table_size
        FROM execution_logs
        UNION ALL
        SELECT 
            'work_orders' as table_name,
            COUNT(*) as record_count,
            pg_size_pretty(pg_total_relation_size('work_orders')) as table_size
        FROM work_orders
        "#
    )
    .fetch_all(&database.pool)
    .await?;

    let mut table_metrics = HashMap::new();
    for metric in metrics {
        table_metrics.insert(metric.table_name, serde_json::json!({
            "record_count": metric.record_count,
            "table_size": metric.table_size
        }));
    }

    Ok(Json(serde_json::json!({
        "table_metrics": table_metrics,
        "timestamp": Utc::now()
    })))
}
