use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON>son,
    Extension,
};
use validator::Validate;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        work_order::{
            WorkOrder, WorkOrderWithDetails, WorkOrderPlanTask,
            CreateWorkOrderRequest, UpdateWorkOrderRequest, WorkOrderQuery
        },
    },
    require_any_role,
};

pub async fn list_work_orders(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<WorkOrderQuery>,
) -> Result<Json<Vec<WorkOrderWithDetails>>> {
    // 计划员、工艺员和管理员可以查看工单列表
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = ((query.page.unwrap_or(1) - 1) as i64) * limit;

    // 简化查询，先获取基本工单信息
    let work_orders_basic = sqlx::query!(
        r#"
        SELECT
            wo.id,
            wo.project_bom_id,
            p.project_name,
            pt.part_number,
            pt.part_name,
            pt.version as part_version,
            wo.quantity,
            wo.status,
            wo.due_date,
            wo.created_at
        FROM work_orders wo
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts pt ON pb.part_id = pt.id
        ORDER BY wo.created_at DESC
        LIMIT $1 OFFSET $2
        "#,
        limit,
        offset
    )
    .fetch_all(&database.pool)
    .await?;

    // 转换为WorkOrderWithDetails结构
    let mut work_orders = Vec::new();
    for wo in work_orders_basic {
        work_orders.push(WorkOrderWithDetails {
            id: wo.id,
            project_bom_id: wo.project_bom_id,
            project_name: wo.project_name,
            part_number: wo.part_number,
            part_name: wo.part_name,
            part_version: wo.part_version,
            quantity: wo.quantity,
            status: wo.status,
            due_date: wo.due_date,
            created_at: wo.created_at,
            plan_tasks: Vec::new(), // 暂时为空，后续可以按需加载
        });
    }

    Ok(Json(work_orders))
}

pub async fn get_work_order(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(work_order_id): Path<i32>,
) -> Result<Json<WorkOrderWithDetails>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    // 获取工单基本信息
    let work_order = sqlx::query!(
        r#"
        SELECT
            wo.id,
            wo.project_bom_id,
            p.project_name,
            pt.part_number,
            pt.part_name,
            pt.version as part_version,
            wo.quantity,
            wo.status,
            wo.due_date,
            wo.created_at
        FROM work_orders wo
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts pt ON pb.part_id = pt.id
        WHERE wo.id = $1
        "#,
        work_order_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Work order not found".to_string()))?;

    // 获取相关的计划任务
    let plan_tasks = sqlx::query_as!(
        WorkOrderPlanTask,
        r#"
        SELECT
            pt.id,
            pt.routing_step_id,
            r.step_number,
            r.process_name,
            sg.group_name as skill_group_name,
            pt.planned_start,
            pt.planned_end,
            pt.status
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN skill_groups sg ON pt.skill_group_id = sg.id
        WHERE pt.work_order_id = $1
        ORDER BY r.step_number
        "#,
        work_order_id
    )
    .fetch_all(&database.pool)
    .await?;

    let work_order_with_details = WorkOrderWithDetails {
        id: work_order.id,
        project_bom_id: work_order.project_bom_id,
        project_name: work_order.project_name,
        part_number: work_order.part_number,
        part_name: work_order.part_name,
        part_version: work_order.part_version,
        quantity: work_order.quantity,
        status: work_order.status,
        due_date: work_order.due_date,
        created_at: work_order.created_at,
        plan_tasks,
    };

    Ok(Json(work_order_with_details))
}

pub async fn create_work_order(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreateWorkOrderRequest>,
) -> Result<Json<WorkOrder>> {
    // 只有计划员、工艺员和管理员可以创建工单
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查项目BOM是否存在
    sqlx::query_scalar!(
        "SELECT id FROM project_boms WHERE id = $1",
        request.project_bom_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Project BOM not found".to_string()))?;

    let work_order = sqlx::query_as!(
        WorkOrder,
        r#"
        INSERT INTO work_orders (project_bom_id, quantity, status, due_date)
        VALUES ($1, $2, $3, $4)
        RETURNING id, project_bom_id, quantity, status, due_date, created_at
        "#,
        request.project_bom_id,
        request.quantity,
        WorkOrder::STATUS_PENDING,
        request.due_date
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(work_order))
}

pub async fn update_work_order(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(work_order_id): Path<i32>,
    Json(request): Json<UpdateWorkOrderRequest>,
) -> Result<Json<WorkOrder>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查工单是否存在
    sqlx::query_scalar!(
        "SELECT id FROM work_orders WHERE id = $1",
        work_order_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Work order not found".to_string()))?;

    let work_order = sqlx::query_as!(
        WorkOrder,
        r#"
        UPDATE work_orders
        SET
            quantity = COALESCE($2, quantity),
            status = COALESCE($3, status),
            due_date = COALESCE($4, due_date)
        WHERE id = $1
        RETURNING id, project_bom_id, quantity, status, due_date, created_at
        "#,
        work_order_id,
        request.quantity,
        request.status,
        request.due_date
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(work_order))
}

pub async fn delete_work_order(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(work_order_id): Path<i32>,
) -> Result<StatusCode> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    // 检查是否有相关的计划任务
    let plan_task_count = sqlx::query_scalar!(
        "SELECT COUNT(*) FROM plan_tasks WHERE work_order_id = $1",
        work_order_id
    )
    .fetch_one(&database.pool)
    .await?;

    if plan_task_count.unwrap_or(0) > 0 {
        return Err(AppError::Validation("Cannot delete work order: it has associated plan tasks".to_string()));
    }

    let deleted_rows = sqlx::query!(
        "DELETE FROM work_orders WHERE id = $1",
        work_order_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if deleted_rows == 0 {
        return Err(AppError::NotFound("Work order not found".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}
