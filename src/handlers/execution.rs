use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use validator::Validate;
use chrono::{DateTime, Utc};
use base64::{Engine as _, engine::general_purpose};

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        execution_log::{
            ExecutionLog, ExecutionLogWithDetails, WorkstationStatus, QRCodeData,
            CreateExecutionLogRequest, ScanReportRequest, ExecutionLogQuery,
            TraceabilityQuery, TraceabilityRecord, ExecutionStep
        },
        plan_task::PlanTask,
    },
    require_any_role,
};

pub async fn list_execution_logs(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ExecutionLogQuery>,
) -> Result<Json<Vec<ExecutionLogWithDetails>>> {
    // 所有角色都可以查看执行日志
    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = ((query.page.unwrap_or(1) - 1) as i64) * limit;

    let execution_logs = sqlx::query_as!(
        ExecutionLogWithDetails,
        r#"
        SELECT
            el.id,
            el.plan_task_id,
            el.machine_id,
            m.machine_name,
            el.user_id,
            u.username,
            u.full_name as user_full_name,
            el.event_type,
            el.event_time,
            el.notes,
            p.project_name,
            parts.part_number,
            parts.part_name,
            r.process_name,
            r.step_number
        FROM execution_logs el
        JOIN users u ON el.user_id = u.id
        LEFT JOIN machines m ON el.machine_id = m.id
        JOIN plan_tasks pt ON el.plan_task_id = pt.id
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        ORDER BY el.event_time DESC
        LIMIT $1 OFFSET $2
        "#,
        limit,
        offset
    )
    .fetch_all(&database.pool)
    .await?;

    Ok(Json(execution_logs))
}

pub async fn get_workstation_status(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<Vec<WorkstationStatus>>> {
    // 操作员、计划员和管理员可以查看工作站状态
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER, Role::OPERATOR);

    let workstation_tasks = sqlx::query!(
        r#"
        SELECT
            pt.id as plan_task_id,
            p.project_name,
            parts.part_number,
            parts.part_name,
            r.process_name,
            r.step_number,
            wo.quantity as work_order_quantity,
            pt.planned_start,
            pt.planned_end,
            pt.status,
            m.machine_name as assigned_machine,
            u.full_name as current_operator,
            COALESCE(
                (SELECT el.event_time FROM execution_logs el
                 WHERE el.plan_task_id = pt.id AND el.event_type = 'START'
                 ORDER BY el.event_time DESC LIMIT 1),
                NULL
            ) as actual_start
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        LEFT JOIN machines m ON pt.id = (
            SELECT el.plan_task_id FROM execution_logs el
            WHERE el.plan_task_id = pt.id AND el.machine_id = m.id
            ORDER BY el.event_time DESC LIMIT 1
        )
        LEFT JOIN users u ON pt.id = (
            SELECT el.plan_task_id FROM execution_logs el
            WHERE el.plan_task_id = pt.id AND el.user_id = u.id
            ORDER BY el.event_time DESC LIMIT 1
        )
        WHERE pt.status IN ('ASSIGNED', 'IN_PROGRESS')
        ORDER BY pt.planned_start
        "#
    )
    .fetch_all(&database.pool)
    .await?;

    let mut workstation_status = Vec::new();
    for task in workstation_tasks {
        let progress_percentage = match task.status.as_str() {
            "IN_PROGRESS" => {
                // 简单的进度计算：如果已开始，假设进度为50%
                if task.actual_start.is_some() {
                    50.0
                } else {
                    0.0
                }
            }
            "ASSIGNED" => 25.0,
            _ => 0.0,
        };

        workstation_status.push(WorkstationStatus {
            plan_task_id: task.plan_task_id,
            project_name: task.project_name,
            part_number: task.part_number,
            part_name: task.part_name,
            process_name: task.process_name,
            step_number: task.step_number,
            work_order_quantity: task.work_order_quantity,
            planned_start: task.planned_start,
            planned_end: task.planned_end,
            actual_start: task.actual_start,
            status: task.status,
            assigned_machine: task.assigned_machine,
            current_operator: task.current_operator,
            progress_percentage,
        });
    }

    Ok(Json(workstation_status))
}

pub async fn generate_qr_code(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(task_id): Path<i32>,
) -> Result<Json<serde_json::Value>> {
    // 计划员和管理员可以生成二维码
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    // 检查任务是否存在
    sqlx::query_scalar!(
        "SELECT id FROM plan_tasks WHERE id = $1",
        task_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Plan task not found".to_string()))?;

    let qr_data = QRCodeData {
        task_id,
        task_type: "PLAN_TASK".to_string(),
        timestamp: Utc::now(),
    };

    let qr_json = serde_json::to_string(&qr_data)
        .map_err(|_| AppError::Internal("Failed to serialize QR data".to_string()))?;

    let qr_code = general_purpose::STANDARD.encode(qr_json.as_bytes());

    Ok(Json(serde_json::json!({
        "task_id": task_id,
        "qr_code": qr_code,
        "generated_at": Utc::now(),
        "expires_at": Utc::now() + chrono::Duration::hours(24)
    })))
}

pub async fn scan_report(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<ScanReportRequest>,
) -> Result<Json<ExecutionLog>> {
    // 操作员可以扫码报工
    require_any_role!(current_user, Role::ADMIN, Role::OPERATOR);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 解析二维码
    let qr_bytes = general_purpose::STANDARD.decode(&request.qr_code)
        .map_err(|_| AppError::Validation("Invalid QR code format".to_string()))?;

    let qr_json = String::from_utf8(qr_bytes)
        .map_err(|_| AppError::Validation("Invalid QR code encoding".to_string()))?;

    let qr_data: QRCodeData = serde_json::from_str(&qr_json)
        .map_err(|_| AppError::Validation("Invalid QR code data".to_string()))?;

    // 检查二维码是否过期（24小时）
    let now = Utc::now();
    if now.signed_duration_since(qr_data.timestamp).num_hours() > 24 {
        return Err(AppError::Validation("QR code has expired".to_string()));
    }

    // 检查任务是否存在
    let plan_task = sqlx::query_as!(
        PlanTask,
        "SELECT id, work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status FROM plan_tasks WHERE id = $1",
        qr_data.task_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Plan task not found".to_string()))?;

    // 开始事务
    let mut tx = database.pool.begin().await?;

    // 根据事件类型更新任务状态
    let new_status = match request.event_type.as_str() {
        "START" => {
            if plan_task.status != "ASSIGNED" && plan_task.status != "PLANNED" {
                return Err(AppError::Validation("Task cannot be started in current status".to_string()));
            }
            "IN_PROGRESS"
        }
        "COMPLETE" => {
            if plan_task.status != "IN_PROGRESS" {
                return Err(AppError::Validation("Task must be in progress to complete".to_string()));
            }
            "COMPLETED"
        }
        "PAUSE" => {
            if plan_task.status != "IN_PROGRESS" {
                return Err(AppError::Validation("Task must be in progress to pause".to_string()));
            }
            "ASSIGNED"
        }
        "RESUME" => {
            if plan_task.status != "ASSIGNED" {
                return Err(AppError::Validation("Task must be paused to resume".to_string()));
            }
            "IN_PROGRESS"
        }
        _ => plan_task.status.as_str(),
    };

    // 更新任务状态
    sqlx::query!(
        "UPDATE plan_tasks SET status = $1 WHERE id = $2",
        new_status,
        qr_data.task_id
    )
    .execute(&mut *tx)
    .await?;

    // 创建执行记录
    let execution_log = sqlx::query_as!(
        ExecutionLog,
        r#"
        INSERT INTO execution_logs (plan_task_id, machine_id, user_id, event_type, event_time, notes)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, plan_task_id, machine_id, user_id, event_type, event_time, notes
        "#,
        qr_data.task_id,
        request.machine_id,
        current_user.id,
        request.event_type,
        now,
        request.notes
    )
    .fetch_one(&mut *tx)
    .await?;

    // 如果任务完成，检查是否需要更新工单状态
    if new_status == "COMPLETED" {
        let remaining_tasks = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM plan_tasks pt
            WHERE pt.work_order_id = $1 AND pt.status != 'COMPLETED' AND pt.status != 'CANCELLED'
            "#,
            plan_task.work_order_id
        )
        .fetch_one(&mut *tx)
        .await?;

        if remaining_tasks.unwrap_or(1) == 0 {
            // 所有任务都完成了，更新工单状态
            sqlx::query!(
                "UPDATE work_orders SET status = 'COMPLETED' WHERE id = $1",
                plan_task.work_order_id
            )
            .execute(&mut *tx)
            .await?;
        }
    }

    tx.commit().await?;

    Ok(Json(execution_log))
}

pub async fn create_execution_log(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreateExecutionLogRequest>,
) -> Result<Json<ExecutionLog>> {
    // 操作员可以手动创建执行记录
    require_any_role!(current_user, Role::ADMIN, Role::OPERATOR);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查任务是否存在
    sqlx::query_scalar!(
        "SELECT id FROM plan_tasks WHERE id = $1",
        request.plan_task_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Plan task not found".to_string()))?;

    let execution_log = sqlx::query_as!(
        ExecutionLog,
        r#"
        INSERT INTO execution_logs (plan_task_id, machine_id, user_id, event_type, event_time, notes)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, plan_task_id, machine_id, user_id, event_type, event_time, notes
        "#,
        request.plan_task_id,
        request.machine_id,
        current_user.id,
        request.event_type,
        Utc::now(),
        request.notes
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(execution_log))
}

pub async fn get_traceability(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<TraceabilityQuery>,
) -> Result<Json<Vec<TraceabilityRecord>>> {
    // 所有角色都可以查看追溯信息

    let traceability_data = sqlx::query!(
        r#"
        SELECT
            wo.id as work_order_id,
            p.project_name,
            parts.part_number,
            parts.part_name,
            wo.quantity
        FROM work_orders wo
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        WHERE ($1::text IS NULL OR parts.part_number ILIKE $1)
        AND ($2::integer IS NULL OR wo.id = $2)
        AND ($3::integer IS NULL OR p.id = $3)
        ORDER BY wo.created_at DESC
        LIMIT 50
        "#,
        query.part_number.as_ref().map(|s| format!("%{}%", s)),
        query.work_order_id,
        query.project_id
    )
    .fetch_all(&database.pool)
    .await?;

    let mut traceability_records = Vec::new();

    for wo_data in traceability_data {
        // 获取该工单的执行历史
        let execution_history = sqlx::query!(
            r#"
            SELECT
                r.step_number,
                r.process_name,
                u.full_name as operator,
                m.machine_name,
                pt.status,
                MIN(CASE WHEN el.event_type = 'START' THEN el.event_time END) as start_time,
                MAX(CASE WHEN el.event_type = 'COMPLETE' THEN el.event_time END) as end_time,
                ARRAY_AGG(el.notes ORDER BY el.event_time) FILTER (WHERE el.notes IS NOT NULL) as notes
            FROM plan_tasks pt
            JOIN routings r ON pt.routing_step_id = r.id
            LEFT JOIN execution_logs el ON pt.id = el.plan_task_id
            LEFT JOIN users u ON el.user_id = u.id
            LEFT JOIN machines m ON el.machine_id = m.id
            WHERE pt.work_order_id = $1
            GROUP BY r.step_number, r.process_name, u.full_name, m.machine_name, pt.status
            ORDER BY r.step_number
            "#,
            wo_data.work_order_id
        )
        .fetch_all(&database.pool)
        .await?;

        let mut execution_steps = Vec::new();
        for step in execution_history {
            let duration_hours = if let (Some(start), Some(end)) = (step.start_time, step.end_time) {
                Some((end - start).num_minutes() as f32 / 60.0)
            } else {
                None
            };

            execution_steps.push(ExecutionStep {
                step_number: step.step_number,
                process_name: step.process_name,
                operator: step.operator.unwrap_or_else(|| "未分配".to_string()),
                machine: step.machine_name,
                start_time: step.start_time,
                end_time: step.end_time,
                duration_hours,
                status: step.status,
                notes: step.notes.unwrap_or_default(),
            });
        }

        traceability_records.push(TraceabilityRecord {
            work_order_id: wo_data.work_order_id,
            project_name: wo_data.project_name,
            part_number: wo_data.part_number,
            part_name: wo_data.part_name,
            quantity: wo_data.quantity,
            execution_history: execution_steps,
        });
    }

    Ok(Json(traceability_records))
}

pub async fn get_my_tasks(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<Vec<WorkstationStatus>>> {
    // 操作员查看分配给自己的任务
    require_any_role!(current_user, Role::ADMIN, Role::OPERATOR);

    // 获取用户的技能组
    let user_skills = sqlx::query_scalar!(
        r#"
        SELECT sg.id
        FROM skill_groups sg
        JOIN user_skills us ON sg.id = us.skill_group_id
        WHERE us.user_id = $1
        "#,
        current_user.id
    )
    .fetch_all(&database.pool)
    .await?;

    if user_skills.is_empty() {
        return Ok(Json(Vec::new()));
    }

    let my_tasks = sqlx::query!(
        r#"
        SELECT
            pt.id as plan_task_id,
            p.project_name,
            parts.part_number,
            parts.part_name,
            r.process_name,
            r.step_number,
            wo.quantity as work_order_quantity,
            pt.planned_start,
            pt.planned_end,
            pt.status,
            m.machine_name as assigned_machine,
            COALESCE(
                (SELECT el.event_time FROM execution_logs el
                 WHERE el.plan_task_id = pt.id AND el.event_type = 'START'
                 ORDER BY el.event_time DESC LIMIT 1),
                NULL
            ) as actual_start
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        LEFT JOIN machines m ON pt.id = (
            SELECT el.plan_task_id FROM execution_logs el
            WHERE el.plan_task_id = pt.id AND el.machine_id = m.id
            ORDER BY el.event_time DESC LIMIT 1
        )
        WHERE pt.skill_group_id = ANY($1)
        AND pt.status IN ('ASSIGNED', 'IN_PROGRESS', 'PLANNED')
        ORDER BY pt.planned_start
        "#,
        &user_skills
    )
    .fetch_all(&database.pool)
    .await?;

    let mut task_status = Vec::new();
    for task in my_tasks {
        let progress_percentage = match task.status.as_str() {
            "IN_PROGRESS" => 50.0,
            "ASSIGNED" => 25.0,
            _ => 0.0,
        };

        task_status.push(WorkstationStatus {
            plan_task_id: task.plan_task_id,
            project_name: task.project_name,
            part_number: task.part_number,
            part_name: task.part_name,
            process_name: task.process_name,
            step_number: task.step_number,
            work_order_quantity: task.work_order_quantity,
            planned_start: task.planned_start,
            planned_end: task.planned_end,
            actual_start: task.actual_start,
            status: task.status,
            assigned_machine: task.assigned_machine,
            current_operator: Some(current_user.username.clone()),
            progress_percentage,
        });
    }

    Ok(Json(task_status))
}

pub async fn get_execution_summary(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<serde_json::Value>> {
    // 管理员和计划员可以查看执行摘要
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    let summary = sqlx::query!(
        r#"
        SELECT
            COUNT(CASE WHEN pt.status = 'PLANNED' THEN 1 END) as planned_tasks,
            COUNT(CASE WHEN pt.status = 'ASSIGNED' THEN 1 END) as assigned_tasks,
            COUNT(CASE WHEN pt.status = 'IN_PROGRESS' THEN 1 END) as in_progress_tasks,
            COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN wo.status = 'COMPLETED' THEN 1 END) as completed_work_orders,
            COUNT(CASE WHEN wo.status = 'IN_PROGRESS' THEN 1 END) as active_work_orders
        FROM plan_tasks pt
        JOIN work_orders wo ON pt.work_order_id = wo.id
        WHERE wo.created_at >= CURRENT_DATE - INTERVAL '30 days'
        "#
    )
    .fetch_one(&database.pool)
    .await?;

    let result = serde_json::json!({
        "execution_summary": {
            "planned_tasks": summary.planned_tasks.unwrap_or(0),
            "assigned_tasks": summary.assigned_tasks.unwrap_or(0),
            "in_progress_tasks": summary.in_progress_tasks.unwrap_or(0),
            "completed_tasks": summary.completed_tasks.unwrap_or(0),
            "completed_work_orders": summary.completed_work_orders.unwrap_or(0),
            "active_work_orders": summary.active_work_orders.unwrap_or(0)
        }
    });

    Ok(Json(result))
}
