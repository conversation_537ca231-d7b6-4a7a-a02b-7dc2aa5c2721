use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON>son,
    Extension,
};
use serde::Deserialize;
use validator::Validate;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        project::{
            Project, ProjectWithBom, ProjectBom, ProjectBomItem,
            CreateProjectRequest, UpdateProjectRequest, AddBomItemRequest, UpdateBomItemRequest
        },
    },
    require_any_role,
};

#[derive(Deserialize)]
pub struct ListProjectsQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
}

pub async fn list_projects(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ListProjectsQuery>,
) -> Result<Json<Vec<Project>>> {
    // 工艺员、计划员和管理员可以查看项目列表
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = ((query.page.unwrap_or(1) - 1) as i64) * limit;

    let projects = if let Some(search) = query.search {
        sqlx::query_as!(
            Project,
            r#"
            SELECT id, project_name, customer_name, created_at
            FROM projects
            WHERE project_name ILIKE $1 OR customer_name ILIKE $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            format!("%{}%", search),
            limit,
            offset
        )
        .fetch_all(&database.pool)
        .await?
    } else {
        sqlx::query_as!(
            Project,
            r#"
            SELECT id, project_name, customer_name, created_at
            FROM projects
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset
        )
        .fetch_all(&database.pool)
        .await?
    };

    Ok(Json(projects))
}

pub async fn get_project(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(project_id): Path<i32>,
) -> Result<Json<ProjectWithBom>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    // 获取项目基本信息
    let project = sqlx::query_as!(
        Project,
        "SELECT id, project_name, customer_name, created_at FROM projects WHERE id = $1",
        project_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Project not found".to_string()))?;

    // 获取项目BOM信息
    let bom_items = sqlx::query_as!(
        ProjectBomItem,
        r#"
        SELECT
            pb.id,
            pb.part_id,
            p.part_number,
            p.part_name,
            p.version,
            pb.quantity,
            p.specifications
        FROM project_boms pb
        JOIN parts p ON pb.part_id = p.id
        WHERE pb.project_id = $1
        ORDER BY p.part_number
        "#,
        project_id
    )
    .fetch_all(&database.pool)
    .await?;

    let project_with_bom = ProjectWithBom {
        id: project.id,
        project_name: project.project_name,
        customer_name: project.customer_name,
        created_at: project.created_at,
        bom_items,
    };

    Ok(Json(project_with_bom))
}

pub async fn create_project(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreateProjectRequest>,
) -> Result<Json<Project>> {
    // 只有工艺员和管理员可以创建项目
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    let project = sqlx::query_as!(
        Project,
        r#"
        INSERT INTO projects (project_name, customer_name)
        VALUES ($1, $2)
        RETURNING id, project_name, customer_name, created_at
        "#,
        request.project_name,
        request.customer_name
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(project))
}

pub async fn update_project(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(project_id): Path<i32>,
    Json(request): Json<UpdateProjectRequest>,
) -> Result<Json<Project>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查项目是否存在
    let existing_project = sqlx::query_scalar!(
        "SELECT id FROM projects WHERE id = $1",
        project_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Project not found".to_string()))?;

    let project = sqlx::query_as!(
        Project,
        r#"
        UPDATE projects
        SET
            project_name = COALESCE($2, project_name),
            customer_name = COALESCE($3, customer_name)
        WHERE id = $1
        RETURNING id, project_name, customer_name, created_at
        "#,
        project_id,
        request.project_name,
        request.customer_name
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(project))
}

pub async fn delete_project(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(project_id): Path<i32>,
) -> Result<StatusCode> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    let deleted_rows = sqlx::query!(
        "DELETE FROM projects WHERE id = $1",
        project_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if deleted_rows == 0 {
        return Err(AppError::NotFound("Project not found".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}

// BOM管理功能
pub async fn add_bom_item(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(project_id): Path<i32>,
    Json(request): Json<AddBomItemRequest>,
) -> Result<Json<ProjectBomItem>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查项目是否存在
    sqlx::query_scalar!(
        "SELECT id FROM projects WHERE id = $1",
        project_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Project not found".to_string()))?;

    // 检查零件是否存在
    sqlx::query_scalar!(
        "SELECT id FROM parts WHERE id = $1",
        request.part_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Part not found".to_string()))?;

    // 检查是否已经存在相同的BOM项
    let existing_bom = sqlx::query_scalar!(
        "SELECT id FROM project_boms WHERE project_id = $1 AND part_id = $2",
        project_id,
        request.part_id
    )
    .fetch_optional(&database.pool)
    .await?;

    if existing_bom.is_some() {
        return Err(AppError::Validation("Part already exists in project BOM".to_string()));
    }

    // 添加BOM项
    let bom_id = sqlx::query_scalar!(
        r#"
        INSERT INTO project_boms (project_id, part_id, quantity)
        VALUES ($1, $2, $3)
        RETURNING id
        "#,
        project_id,
        request.part_id,
        request.quantity
    )
    .fetch_one(&database.pool)
    .await?;

    // 获取完整的BOM项信息
    let bom_item = sqlx::query_as!(
        ProjectBomItem,
        r#"
        SELECT
            pb.id,
            pb.part_id,
            p.part_number,
            p.part_name,
            p.version,
            pb.quantity,
            p.specifications
        FROM project_boms pb
        JOIN parts p ON pb.part_id = p.id
        WHERE pb.id = $1
        "#,
        bom_id
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(bom_item))
}

pub async fn update_bom_item(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path((project_id, bom_id)): Path<(i32, i32)>,
    Json(request): Json<UpdateBomItemRequest>,
) -> Result<Json<ProjectBomItem>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 更新BOM项数量
    let updated_rows = sqlx::query!(
        r#"
        UPDATE project_boms
        SET quantity = $1
        WHERE id = $2 AND project_id = $3
        "#,
        request.quantity,
        bom_id,
        project_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if updated_rows == 0 {
        return Err(AppError::NotFound("BOM item not found".to_string()));
    }

    // 获取更新后的BOM项信息
    let bom_item = sqlx::query_as!(
        ProjectBomItem,
        r#"
        SELECT
            pb.id,
            pb.part_id,
            p.part_number,
            p.part_name,
            p.version,
            pb.quantity,
            p.specifications
        FROM project_boms pb
        JOIN parts p ON pb.part_id = p.id
        WHERE pb.id = $1
        "#,
        bom_id
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(bom_item))
}

pub async fn remove_bom_item(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path((project_id, bom_id)): Path<(i32, i32)>,
) -> Result<StatusCode> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    let deleted_rows = sqlx::query!(
        "DELETE FROM project_boms WHERE id = $1 AND project_id = $2",
        bom_id,
        project_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if deleted_rows == 0 {
        return Err(AppError::NotFound("BOM item not found".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}
