use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use bcrypt::{hash, DEFAULT_COST};
use serde::Deserialize;
use validator::Validate;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        user::{CreateUserRequest, UpdateUserRequest, UserResponse},
    },
    require_any_role,
};

#[derive(Deserialize)]
pub struct ListUsersQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
}

pub async fn list_users(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ListUsersQuery>,
) -> Result<Json<Vec<UserResponse>>> {
    // 只有管理员和工艺员可以查看用户列表
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = ((query.page.unwrap_or(1) - 1) as i64) * limit;

    let search_pattern = query.search.map(|s| format!("%{}%", s));

    let users = sqlx::query!(
        r#"
        SELECT id, username, full_name, is_active, created_at
        FROM users
        WHERE ($1::text IS NULL OR username ILIKE $1 OR full_name ILIKE $1)
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
        "#,
        search_pattern,
        limit,
        offset
    )
    .fetch_all(&database.pool)
    .await?;

    let mut user_responses = Vec::new();

    for user in users {
        // 获取用户角色
        let roles = sqlx::query_scalar!(
            r#"
            SELECT r.role_name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1
            "#,
            user.id
        )
        .fetch_all(&database.pool)
        .await?;

        // 获取用户技能
        let skills = sqlx::query_scalar!(
            r#"
            SELECT sg.group_name
            FROM skill_groups sg
            JOIN user_skills us ON sg.id = us.skill_group_id
            WHERE us.user_id = $1
            "#,
            user.id
        )
        .fetch_all(&database.pool)
        .await?;

        user_responses.push(UserResponse {
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            is_active: user.is_active,
            created_at: user.created_at,
            roles,
            skills,
        });
    }

    Ok(Json(user_responses))
}

pub async fn get_user(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(user_id): Path<i32>,
) -> Result<Json<UserResponse>> {
    // 用户只能查看自己的信息，或者管理员可以查看所有用户
    if current_user.id != user_id && !current_user.is_admin() {
        return Err(AppError::Authorization("Access denied".to_string()));
    }

    let user = sqlx::query!(
        "SELECT id, username, full_name, is_active, created_at FROM users WHERE id = $1",
        user_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

    // 获取用户角色
    let roles = sqlx::query_scalar!(
        r#"
        SELECT r.role_name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
        "#,
        user.id
    )
    .fetch_all(&database.pool)
    .await?;

    // 获取用户技能
    let skills = sqlx::query_scalar!(
        r#"
        SELECT sg.group_name
        FROM skill_groups sg
        JOIN user_skills us ON sg.id = us.skill_group_id
        WHERE us.user_id = $1
        "#,
        user.id
    )
    .fetch_all(&database.pool)
    .await?;

    Ok(Json(UserResponse {
        id: user.id,
        username: user.username,
        full_name: user.full_name,
        is_active: user.is_active,
        created_at: user.created_at,
        roles,
        skills,
    }))
}

pub async fn create_user(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreateUserRequest>,
) -> Result<Json<UserResponse>> {
    // 只有管理员可以创建用户
    require_any_role!(current_user, Role::ADMIN);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查用户名是否已存在
    let existing_user = sqlx::query_scalar!(
        "SELECT id FROM users WHERE username = $1",
        request.username
    )
    .fetch_optional(&database.pool)
    .await?;

    if existing_user.is_some() {
        return Err(AppError::Validation("Username already exists".to_string()));
    }

    // 哈希密码
    let password_hash = hash(&request.password, DEFAULT_COST)
        .map_err(|_| AppError::Internal("Password hashing failed".to_string()))?;

    // 开始事务
    let mut tx = database.pool.begin().await?;

    // 创建用户
    let user_id = sqlx::query_scalar!(
        r#"
        INSERT INTO users (username, password_hash, full_name, is_active)
        VALUES ($1, $2, $3, true)
        RETURNING id
        "#,
        request.username,
        password_hash,
        request.full_name
    )
    .fetch_one(&mut *tx)
    .await?;

    // 分配角色
    for role_id in &request.role_ids {
        sqlx::query!(
            "INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2)",
            user_id,
            role_id
        )
        .execute(&mut *tx)
        .await?;
    }

    // 分配技能
    for skill_group_id in &request.skill_group_ids {
        sqlx::query!(
            "INSERT INTO user_skills (user_id, skill_group_id) VALUES ($1, $2)",
            user_id,
            skill_group_id
        )
        .execute(&mut *tx)
        .await?;
    }

    tx.commit().await?;

    // 获取创建的用户信息
    get_user(Extension(current_user), State(database), Path(user_id)).await
}
