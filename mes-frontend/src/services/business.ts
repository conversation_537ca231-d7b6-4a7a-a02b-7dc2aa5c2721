import { apiRequest } from './api';
import {
  Project,
  WorkOrder,
  PlanTask,
  ExecutionLog,
  DashboardStats,
  PaginatedResponse,
  PaginationParams,
  LoginRequest,
  LoginResponse,
  UserInfo
} from '../types';

// 项目服务 - 与后端API保持一致
export const projectService = {
  // 获取项目列表 - 后端返回简单数组，不是分页格式
  getProjects: (params?: PaginationParams) =>
    apiRequest.get<Project[]>('/projects', params),

  // 获取项目详情
  getProject: (id: number) =>
    apiRequest.get<Project>(`/projects/${id}`),

  // 创建项目
  createProject: (data: Partial<Project>) =>
    apiRequest.post<Project>('/projects', data),

  // 更新项目
  updateProject: (id: number, data: Partial<Project>) =>
    apiRequest.put<Project>(`/projects/${id}`, data),

  // 删除项目
  deleteProject: (id: number) =>
    apiRequest.delete(`/projects/${id}`),
};

// 工单服务 - 与后端API保持一致
export const workOrderService = {
  // 获取工单列表 - 后端返回简单数组，不是分页格式
  getWorkOrders: (params?: PaginationParams & { status?: string; project_id?: number }) =>
    apiRequest.get<WorkOrder[]>('/work-orders', params),

  // 获取工单详情
  getWorkOrder: (id: number) =>
    apiRequest.get<WorkOrder>(`/work-orders/${id}`),

  // 创建工单
  createWorkOrder: (data: Partial<WorkOrder>) =>
    apiRequest.post<WorkOrder>('/work-orders', data),

  // 更新工单
  updateWorkOrder: (id: number, data: Partial<WorkOrder>) =>
    apiRequest.put<WorkOrder>(`/work-orders/${id}`, data),

  // 删除工单
  deleteWorkOrder: (id: number) =>
    apiRequest.delete(`/work-orders/${id}`),

  // 启动工单
  startWorkOrder: (id: number) =>
    apiRequest.post(`/work-orders/${id}/start`),

  // 完成工单
  completeWorkOrder: (id: number) =>
    apiRequest.post(`/work-orders/${id}/complete`),
};

// 计划任务服务
export const planTaskService = {
  // 获取任务列表
  getTasks: (params?: PaginationParams & { work_order_id?: number; status?: string }) => 
    apiRequest.get<PaginatedResponse<PlanTask>>('/plan-tasks', params),

  // 获取任务详情
  getTask: (id: number) => 
    apiRequest.get<PlanTask>(`/plan-tasks/${id}`),

  // 创建任务
  createTask: (data: Partial<PlanTask>) => 
    apiRequest.post<PlanTask>('/plan-tasks', data),

  // 更新任务
  updateTask: (id: number, data: Partial<PlanTask>) => 
    apiRequest.put<PlanTask>(`/plan-tasks/${id}`, data),

  // 删除任务
  deleteTask: (id: number) => 
    apiRequest.delete(`/plan-tasks/${id}`),

  // 自动调度任务
  autoSchedule: (workOrderId: number) => 
    apiRequest.post(`/plan-tasks/auto-schedule`, { work_order_id: workOrderId }),

  // 获取我的任务
  getMyTasks: () => 
    apiRequest.get<PlanTask[]>('/execution/my-tasks'),
};

// 执行服务
export const executionService = {
  // 获取执行日志列表
  getExecutionLogs: (params?: any) =>
    apiRequest.get<ExecutionLog[]>('/execution-logs', params),

  // 创建执行日志
  createExecutionLog: (data: any) =>
    apiRequest.post('/execution-logs', data),

  // 获取工作站状态
  getWorkstationStatus: () =>
    apiRequest.get('/execution/workstation-status'),

  // 获取我的任务
  getMyTasks: () =>
    apiRequest.get('/execution/my-tasks'),

  // 获取执行摘要
  getExecutionSummary: () =>
    apiRequest.get('/execution/summary'),

  // 生成二维码
  generateQRCode: (taskId: number) =>
    apiRequest.get(`/execution/qr-code/${taskId}`),

  // 扫码报工
  scanReport: (data: any) =>
    apiRequest.post('/execution/scan-report', data),

  // 获取追溯信息
  getTraceability: (params?: any) =>
    apiRequest.get('/execution/traceability', params),

  // 任务操作方法 - 前端组件需要的方法
  startTask: (taskId: number, notes?: string) =>
    apiRequest.post(`/execution/tasks/${taskId}/start`, { notes }),

  pauseTask: (taskId: number, notes?: string) =>
    apiRequest.post(`/execution/tasks/${taskId}/pause`, { notes }),

  resumeTask: (taskId: number, notes?: string) =>
    apiRequest.post(`/execution/tasks/${taskId}/resume`, { notes }),

  completeTask: (taskId: number, notes?: string, qualityData?: any) =>
    apiRequest.post(`/execution/tasks/${taskId}/complete`, { notes, quality_data: qualityData }),

  reportIssue: (taskId: number, notes: string) =>
    apiRequest.post(`/execution/tasks/${taskId}/issue`, { notes }),
};

// 仪表板服务
export const dashboardService = {
  // 获取仪表板统计数据
  getStats: () =>
    apiRequest.get<DashboardStats>('/analytics/kpi-dashboard'),

  // 获取趋势分析数据
  getTrends: (params?: any) =>
    apiRequest.get('/analytics/trends', params),

  // 获取实时指标数据
  getRealTimeMetrics: () =>
    apiRequest.get('/analytics/real-time'),

  // 获取对比分析数据
  getComparison: (params?: any) =>
    apiRequest.get('/analytics/comparison', params),
};

// 系统服务
export const systemService = {
  // 获取系统健康状态
  getSystemHealth: () =>
    apiRequest.get('/system/health'),

  // 获取系统指标
  getSystemMetrics: () =>
    apiRequest.get('/system/metrics'),

  // 获取系统优化建议
  getSystemOptimization: () =>
    apiRequest.get('/system/optimization'),
};

// 用户服务
export const userService = {
  getUsers: (params?: any) => apiRequest.get('/users', params),
  getUser: (id: number) => apiRequest.get(`/users/${id}`),
  createUser: (data: any) => apiRequest.post('/users', data),
};

// 零件服务
export const partService = {
  getParts: (params?: any) => apiRequest.get('/parts', params),
  getPart: (id: number) => apiRequest.get(`/parts/${id}`),
  createPart: (data: any) => apiRequest.post('/parts', data),
  updatePart: (id: number, data: any) => apiRequest.put(`/parts/${id}`, data),
  deletePart: (id: number) => apiRequest.delete(`/parts/${id}`),
};

// 认证服务
export const authService = {
  // 用户登录
  login: (credentials: LoginRequest) =>
    apiRequest.post<LoginResponse>('/auth/login', credentials),

  // 用户登出
  logout: () =>
    apiRequest.post('/auth/logout'),

  // 获取当前用户信息
  getCurrentUser: (): UserInfo | null => {
    const userStr = localStorage.getItem('currentUser');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  },

  // 保存用户信息
  setCurrentUser: (user: UserInfo) => {
    localStorage.setItem('currentUser', JSON.stringify(user));
  },

  // 清除用户信息
  clearCurrentUser: () => {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('token');
  },

  // 获取Token
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  // 保存Token
  setToken: (token: string) => {
    localStorage.setItem('token', token);
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    const token = authService.getToken();
    const user = authService.getCurrentUser();
    return !!(token && user);
  },
};
