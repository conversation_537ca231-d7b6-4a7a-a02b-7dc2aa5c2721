import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

interface UseApiOptions<T> {
  immediate?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  deps?: any[];
  pollingInterval?: number;
}

interface UseApiResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<T | null>;
  refresh: () => Promise<T | null>;
  reset: () => void;
}

export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<{ success: boolean; data?: T; message?: string }>,
  options: UseApiOptions<T> = {}
): UseApiResult<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    deps = [],
    pollingInterval,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastArgs, setLastArgs] = useState<any[]>([]);
  
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    if (!mountedRef.current) return null;
    
    setLoading(true);
    setError(null);
    setLastArgs(args);

    try {
      const response = await apiFunction(...args);
      
      if (!mountedRef.current) return null;

      if (response.success && response.data !== undefined) {
        setData(response.data);
        onSuccess?.(response.data);
        return response.data;
      } else {
        const errorMsg = response.message || 'API调用失败';
        const error = new Error(errorMsg);
        setError(error);
        onError?.(error);
        return null;
      }
    } catch (err) {
      if (!mountedRef.current) return null;
      
      const error = err instanceof Error ? err : new Error('未知错误');
      setError(error);
      onError?.(error);
      return null;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiFunction, onSuccess, onError]);

  const refresh = useCallback(() => {
    return execute(...lastArgs);
  }, [execute, lastArgs]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
    setLastArgs([]);
    
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // 轮询功能
  useEffect(() => {
    if (pollingInterval && data !== null) {
      pollingRef.current = setInterval(() => {
        refresh();
      }, pollingInterval);

      return () => {
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
        }
      };
    }
  }, [pollingInterval, data, refresh]);

  // 依赖变化时重新执行
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, ...deps]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    refresh,
    reset,
  };
}

// 专门用于列表数据的Hook
export function useApiList<T>(
  apiFunction: (...args: any[]) => Promise<{ success: boolean; data?: { data: T[]; total: number }; message?: string }>,
  options: UseApiOptions<{ data: T[]; total: number }> = {}
) {
  const result = useApi(apiFunction, options);
  
  return {
    ...result,
    list: result.data?.data || [],
    total: result.data?.total || 0,
  };
}

// 用于分页数据的Hook - 适配后端返回简单数组的情况
export function usePagination<T>(
  apiFunction: (params?: any) => Promise<{ success: boolean; data?: T[] | { data: T[]; total: number }; message?: string }>,
  initialParams: any = {}
) {
  const [params, setParams] = useState({ page: 1, limit: 10, ...initialParams });

  const { data, loading, error, execute, refresh } = useApi(
    (searchParams) => apiFunction({ ...params, ...searchParams }),
    { immediate: true, deps: [params] }
  );

  const changePage = useCallback((page: number, pageSize?: number) => {
    setParams((prev: any) => ({
      ...prev,
      page,
      ...(pageSize && { limit: pageSize }),
    }));
  }, []);

  const changeParams = useCallback((newParams: any) => {
    setParams((prev: any) => ({
      ...prev,
      ...newParams,
      page: 1, // 重置到第一页
    }));
  }, []);

  // 处理后端返回简单数组或分页对象的情况
  const list = Array.isArray(data) ? data : (data?.data || []);
  const total = Array.isArray(data) ? data.length : (data?.total || 0);

  return {
    list,
    total,
    loading,
    error,
    params,
    changePage,
    changeParams,
    refresh: () => refresh(),
    execute: (searchParams?: any) => execute(searchParams),
  };
}
