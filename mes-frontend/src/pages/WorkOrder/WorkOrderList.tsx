import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { workOrderService } from '../../services/business';
import { WorkOrder } from '../../types';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const WorkOrderList: React.FC = () => {
  const navigate = useNavigate();
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    search: '',
  });

  // 模拟工单数据
  const mockWorkOrders: WorkOrder[] = [
    {
      id: 1,
      project_id: 1,
      part_id: 1,
      quantity: 100,
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      planned_start: '2024-01-01',
      planned_end: '2024-01-15',
      actual_start: '2024-01-01',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      project_name: '汽车零部件生产项目',
      part_number: 'PART-001',
      part_name: '活塞组件',
    },
    {
      id: 2,
      project_id: 1,
      part_id: 2,
      quantity: 50,
      status: 'PLANNED',
      priority: 'MEDIUM',
      planned_start: '2024-01-16',
      planned_end: '2024-02-01',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      project_name: '汽车零部件生产项目',
      part_number: 'PART-002',
      part_name: '连杆组件',
    },
    {
      id: 3,
      project_id: 2,
      part_id: 3,
      quantity: 200,
      status: 'COMPLETED',
      priority: 'LOW',
      planned_start: '2023-12-01',
      planned_end: '2023-12-31',
      actual_start: '2023-12-01',
      actual_end: '2023-12-28',
      created_at: '2023-11-15T00:00:00Z',
      updated_at: '2023-12-28T00:00:00Z',
      project_name: '电子产品外壳项目',
      part_number: 'PART-003',
      part_name: '手机外壳',
    },
  ];

  useEffect(() => {
    fetchWorkOrders();
  }, []);

  const fetchWorkOrders = async () => {
    setLoading(true);
    try {
      const response = await workOrderService.getWorkOrders();
      if (response.success && response.data) {
        // 后端返回的是简单数组，不是分页格式
        setWorkOrders(Array.isArray(response.data) ? response.data : []);
      } else {
        throw new Error('API调用失败');
      }
    } catch (error) {
      console.log('使用模拟数据:', error);
      setWorkOrders(mockWorkOrders);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return '计划中';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'red';
      case 'HIGH':
        return 'orange';
      case 'MEDIUM':
        return 'blue';
      case 'LOW':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return '紧急';
      case 'HIGH':
        return '高';
      case 'MEDIUM':
        return '中';
      case 'LOW':
        return '低';
      default:
        return '未知';
    }
  };

  const calculateProgress = (workOrder: WorkOrder) => {
    if (workOrder.status === 'COMPLETED') return 100;
    if (workOrder.status === 'PLANNED') return 0;
    if (workOrder.status === 'IN_PROGRESS') {
      // 简单的进度计算：基于时间进度
      const start = dayjs(workOrder.actual_start || workOrder.planned_start);
      const end = dayjs(workOrder.planned_end);
      const now = dayjs();
      const total = end.diff(start, 'day');
      const elapsed = now.diff(start, 'day');
      return Math.min(Math.max(Math.round((elapsed / total) * 100), 0), 100);
    }
    return 0;
  };

  const columns = [
    {
      title: '工单编号',
      dataIndex: 'id',
      key: 'id',
      render: (id: number) => (
        <Button
          type="link"
          onClick={() => navigate(`/work-orders/${id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          WO-{id.toString().padStart(4, '0')}
        </Button>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
      ellipsis: true,
    },
    {
      title: '零件信息',
      key: 'part',
      render: (_: any, record: WorkOrder) => (
        <div>
          <div><strong>{record.part_number}</strong></div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.part_name}</div>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'center' as const,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      render: (_: any, record: WorkOrder) => {
        const progress = calculateProgress(record);
        return (
          <div style={{ width: '80px' }}>
            <Progress
              percent={progress}
              size="small"
              status={record.status === 'COMPLETED' ? 'success' : 'active'}
            />
          </div>
        );
      },
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      render: (date: string) => date ? dayjs(date).format('MM-DD') : '-',
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      render: (date: string) => date ? dayjs(date).format('MM-DD') : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: WorkOrder) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/work-orders/${record.id}`)}
            />
          </Tooltip>
          {record.status === 'PLANNED' && (
            <Tooltip title="开始工单">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          {record.status === 'IN_PROGRESS' && (
            <Tooltip title="暂停工单">
              <Button
                type="text"
                icon={<PauseCircleOutlined />}
                style={{ color: '#faad14' }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // 过滤数据
  const filteredWorkOrders = workOrders.filter(wo => {
    const matchesStatus = !filters.status || wo.status === filters.status;
    const matchesPriority = !filters.priority || wo.priority === filters.priority;
    const matchesSearch = !filters.search ||
      wo.project_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      wo.part_number?.toLowerCase().includes(filters.search.toLowerCase()) ||
      wo.part_name?.toLowerCase().includes(filters.search.toLowerCase());

    return matchesStatus && matchesPriority && matchesSearch;
  });

  // 统计数据
  const stats = {
    total: workOrders.length,
    planned: workOrders.filter(wo => wo.status === 'PLANNED').length,
    inProgress: workOrders.filter(wo => wo.status === 'IN_PROGRESS').length,
    completed: workOrders.filter(wo => wo.status === 'COMPLETED').length,
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Title level={2} style={{ margin: 0 }}>
          工单管理
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchWorkOrders}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/work-orders/create')}
          >
            新建工单
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总工单数"
              value={stats.total}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="计划中"
              value={stats.planned}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中"
              value={stats.inProgress}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Search
              placeholder="搜索工单、项目或零件"
              allowClear
              onSearch={(value) => setFilters(prev => ({ ...prev, search: value }))}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value || '' }))}
            >
              <Option value="PLANNED">计划中</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="优先级筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, priority: value || '' }))}
            >
              <Option value="URGENT">紧急</Option>
              <Option value="HIGH">高</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="LOW">低</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 工单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredWorkOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredWorkOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>
    </div>
  );
};

export default WorkOrderList;
