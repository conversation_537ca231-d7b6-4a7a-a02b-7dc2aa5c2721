import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Row,
  Col,
  Table,
  Tag,
  Button,
  Space,
  Statistic,
  Progress,
  Badge,
  Select,
  Input,
  Tooltip,
  Modal,
  Form,
  message,
  Divider,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ToolOutlined,
  ReloadOutlined,
  FilterOutlined,
  SearchOutlined,
  AlertOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { executionService, planTaskService } from '../../services/business';
import { PlanTask, ExecutionLog } from '../../types';
import { useApi } from '../../hooks/useApi';
import RealTimeData from '../../components/Common/RealTimeData';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;
const { TextArea } = Input;

const ExecutionBoard: React.FC = () => {
  const [filters, setFilters] = useState({
    status: '',
    workstation: '',
    search: '',
  });
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'start' | 'pause' | 'resume' | 'complete' | 'issue'>('start');
  const [selectedTask, setSelectedTask] = useState<PlanTask | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasksData, setTasksData] = useState<PlanTask[]>([]);

  // 模拟任务数据 - 使用正确的类型定义
  const mockTasks: PlanTask[] = [
    {
      id: 1,
      work_order_id: 1,
      routing_id: 1,
      step_number: 1,
      process_name: '原料准备',
      assigned_user_id: 1,
      machine_id: 1,
      status: 'IN_PROGRESS',
      planned_start: '2024-01-01T08:00:00Z',
      planned_end: '2024-01-01T10:00:00Z',
      actual_start: '2024-01-01T08:15:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T08:15:00Z',
    },
    {
      id: 2,
      work_order_id: 2,
      routing_id: 2,
      step_number: 2,
      process_name: 'CNC加工',
      assigned_user_id: 2,
      machine_id: 2,
      status: 'PLANNED',
      planned_start: '2024-01-01T10:00:00Z',
      planned_end: '2024-01-01T14:00:00Z',
      standard_hours: 4,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 3,
      work_order_id: 3,
      routing_id: 3,
      step_number: 1,
      process_name: '质量检测',
      assigned_user_id: 3,
      status: 'PLANNED',
      planned_start: '2024-01-01T14:00:00Z',
      planned_end: '2024-01-01T15:00:00Z',
      standard_hours: 1,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 4,
      work_order_id: 4,
      routing_id: 4,
      step_number: 3,
      process_name: '装配',
      assigned_user_id: 1,
      machine_id: 3,
      status: 'COMPLETED',
      planned_start: '2024-01-01T06:00:00Z',
      planned_end: '2024-01-01T08:00:00Z',
      actual_start: '2024-01-01T06:00:00Z',
      actual_end: '2024-01-01T07:45:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T07:45:00Z',
    },
  ];

  // 刷新任务数据
  const refreshTasks = async () => {
    setLoading(true);
    try {
      // 这里可以调用API获取真实数据
      // const response = await planTaskService.getTasks();
      // setTasksData(response.data || []);
      setTasksData(mockTasks); // 暂时使用模拟数据
    } catch (error) {
      message.error('获取任务数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    refreshTasks();
  }, []);

  const tasks = tasksData.length > 0 ? tasksData : mockTasks;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return '计划中';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const handleTaskAction = (task: PlanTask, action: typeof actionType) => {
    setSelectedTask(task);
    setActionType(action);
    form.resetFields();
    setActionModalVisible(true);
  };

  const handleActionSubmit = async (values: any) => {
    if (!selectedTask) return;

    try {
      const { notes, quality_data } = values;

      // 调用对应的API
      switch (actionType) {
        case 'start':
          await executionService.startTask(selectedTask.id, notes);
          message.success('任务已开始');
          break;
        case 'pause':
          await executionService.pauseTask(selectedTask.id, notes);
          message.success('任务已暂停');
          break;
        case 'resume':
          await executionService.resumeTask(selectedTask.id, notes);
          message.success('任务已恢复');
          break;
        case 'complete':
          await executionService.completeTask(selectedTask.id, notes, quality_data);
          message.success('任务已完成');
          break;
        case 'issue':
          await executionService.reportIssue(selectedTask.id, notes);
          message.success('问题已报告');
          break;
      }

      setActionModalVisible(false);
      refreshTasks();
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    const matchesStatus = !filters.status || task.status === filters.status;
    const matchesWorkstation = !filters.workstation || task.machine_id?.toString() === filters.workstation;
    const matchesSearch = !filters.search ||
      task.process_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      task.work_order_id.toString().includes(filters.search);

    return matchesStatus && matchesWorkstation && matchesSearch;
  });

  // 统计数据
  const stats = {
    total: tasks.length,
    planned: tasks.filter(t => t.status === 'PLANNED').length,
    inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,
    completed: tasks.filter(t => t.status === 'COMPLETED').length,
  };

  const calculateTaskProgress = (task: PlanTask) => {
    if (task.status === 'COMPLETED') return 100;
    if (task.status === 'PLANNED') return 0;
    if (task.status === 'IN_PROGRESS' && task.actual_start) {
      const start = dayjs(task.actual_start);
      const end = dayjs(task.planned_end);
      const now = dayjs();
      const total = end.diff(start, 'minute');
      const elapsed = now.diff(start, 'minute');
      return Math.round(Math.random() * 80 + 10); // 模拟进度
    }
    return 0;
  };

  const columns = [
    {
      title: '工单',
      dataIndex: 'work_order_id',
      key: 'work_order_id',
      render: (id: number) => `WO-${id.toString().padStart(4, '0')}`,
    },
    {
      title: '工序',
      dataIndex: 'process_name',
      key: 'process_name',
      render: (name: string, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name || '工序名称'}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            步骤 {record.step_number || 1}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      render: (_: any, record: PlanTask) => {
        const progress = calculateTaskProgress(record);
        return (
          <div style={{ width: '100px' }}>
            <Progress
              percent={progress}
              size="small"
              status={record.status === 'COMPLETED' ? 'success' : 'active'}
            />
          </div>
        );
      },
    },
    {
      title: '操作员',
      dataIndex: 'assigned_user_id',
      key: 'assigned_user_id',
      render: (userId: number) => (
        <Space>
          <UserOutlined />
          {userId ? `操作员${userId}` : '未分配'}
        </Space>
      ),
    },
    {
      title: '设备',
      dataIndex: 'machine_id',
      key: 'machine_id',
      render: (machineId: number) => (
        <Space>
          <ToolOutlined />
          {machineId ? `设备${machineId}` : '无'}
        </Space>
      ),
    },
    {
      title: '计划时间',
      key: 'planned_time',
      render: (_: any, record: PlanTask) => (
        <div>
          <div>{dayjs(record.planned_start).format('HH:mm')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(record.planned_end).format('HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: '标准工时',
      dataIndex: 'standard_hours',
      key: 'standard_hours',
      render: (hours: number) => `${hours}h`,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PlanTask) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>

          {record.status === 'PLANNED' && (
            <Tooltip title="开始任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                style={{ color: '#52c41a' }}
                size="small"
                onClick={() => handleTaskAction(record, 'start')}
              />
            </Tooltip>
          )}

          {record.status === 'IN_PROGRESS' && (
            <>
              <Tooltip title="暂停任务">
                <Button
                  type="text"
                  icon={<PauseCircleOutlined />}
                  style={{ color: '#faad14' }}
                  size="small"
                  onClick={() => handleTaskAction(record, 'pause')}
                />
              </Tooltip>
              <Tooltip title="完成任务">
                <Button
                  type="text"
                  icon={<CheckCircleOutlined />}
                  style={{ color: '#1890ff' }}
                  size="small"
                  onClick={() => handleTaskAction(record, 'complete')}
                />
              </Tooltip>
            </>
          )}

          <Tooltip title="报告问题">
            <Button
              type="text"
              icon={<AlertOutlined />}
              danger
              size="small"
              onClick={() => handleTaskAction(record, 'issue')}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <RealTimeData
      refreshInterval={10000} // 10秒刷新一次
      onRefresh={refreshTasks}
      showIndicator={true}
    >
      <div className="page-container fade-in">
        <div className="page-header">
          <Title level={2} className="page-title">
            车间执行看板
          </Title>
          <Space>
            <Button
              className="action-button"
              icon={<ReloadOutlined />}
              onClick={refreshTasks}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card className="stats-card">
              <Statistic
                title="总任务数"
                value={stats.total}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
                className="statistic-value"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="stats-card">
              <Statistic
                title="计划中"
                value={stats.planned}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
                className="statistic-value"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="stats-card">
              <Statistic
                title="进行中"
                value={stats.inProgress}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
                className="statistic-value"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="stats-card">
              <Statistic
                title="已完成"
                value={stats.completed}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
                className="statistic-value"
              />
            </Card>
          </Col>
        </Row>

        {/* 筛选器 */}
        <Card className="filter-card">
          <Row gutter={16} align="middle" className="filter-row">
            <Col span={6}>
              <Search
                className="search-input"
                placeholder="搜索工序或工单"
                allowClear
                onSearch={(value) => setFilters(prev => ({ ...prev, search: value }))}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="状态筛选"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => setFilters(prev => ({ ...prev, status: value || '' }))}
              >
                <Option value="PLANNED">计划中</Option>
                <Option value="IN_PROGRESS">进行中</Option>
                <Option value="COMPLETED">已完成</Option>
                <Option value="CANCELLED">已取消</Option>
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="工作站筛选"
                allowClear
                style={{ width: '100%' }}
                onChange={(value) => setFilters(prev => ({ ...prev, workstation: value || '' }))}
              >
                <Option value="1">工作站1</Option>
                <Option value="2">工作站2</Option>
                <Option value="3">工作站3</Option>
              </Select>
            </Col>
            <Col span={4}>
              <Space>
                <Badge count={filteredTasks.length} showZero>
                  <Button icon={<FilterOutlined />}>
                    筛选结果
                  </Button>
                </Badge>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 任务列表 */}
        <Card className="content-card">
          <Table
            className="custom-table"
            columns={columns}
            dataSource={filteredTasks}
            rowKey="id"
            loading={loading}
            pagination={{
              className: "custom-pagination",
              total: filteredTasks.length,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            rowClassName={(record) => {
              if (record.status === 'IN_PROGRESS') return 'row-in-progress';
              if (record.status === 'COMPLETED') return 'row-completed';
              return '';
            }}
          />
        </Card>

        {/* 操作模态框 */}
        <Modal
          title={
            actionType === 'start' ? '开始任务' :
            actionType === 'pause' ? '暂停任务' :
            actionType === 'resume' ? '恢复任务' :
            actionType === 'complete' ? '完成任务' : '报告问题'
          }
          open={actionModalVisible}
          onCancel={() => setActionModalVisible(false)}
          footer={null}
          width={500}
        >
          {selectedTask && (
            <div style={{ marginBottom: '16px' }}>
              <Divider orientation="left">任务信息</Divider>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text><strong>工单:</strong> WO-{selectedTask.work_order_id.toString().padStart(4, '0')}</Text>
                <Text><strong>工序:</strong> {selectedTask.process_name || '工序名称'}</Text>
                <Text><strong>步骤:</strong> {selectedTask.step_number || 1}</Text>
                <Text><strong>标准工时:</strong> {selectedTask.standard_hours || 2}h</Text>
              </Space>
            </div>
          )}

          <Form
            form={form}
            layout="vertical"
            onFinish={handleActionSubmit}
          >
            <Form.Item
              name="notes"
              label="备注说明"
              rules={actionType === 'issue' ? [{ required: true, message: '请输入问题描述' }] : []}
            >
              <TextArea
                rows={4}
                placeholder={
                  actionType === 'start' ? '请输入开始备注...' :
                  actionType === 'pause' ? '请输入暂停原因...' :
                  actionType === 'resume' ? '请输入恢复说明...' :
                  actionType === 'complete' ? '请输入完成说明...' : '请详细描述遇到的问题...'
                }
              />
            </Form.Item>

            {actionType === 'complete' && (
              <Form.Item
                name="quality_data"
                label="质量数据"
              >
                <TextArea
                  rows={3}
                  placeholder="请输入质量检测数据..."
                />
              </Form.Item>
            )}

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  确认
                </Button>
                <Button onClick={() => setActionModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </RealTimeData>
  );
};

export default ExecutionBoard;
