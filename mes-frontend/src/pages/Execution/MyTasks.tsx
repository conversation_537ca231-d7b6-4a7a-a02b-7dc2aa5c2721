import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Card,
  Row,
  Col,
  List,
  Tag,
  Button,
  Space,
  Statistic,
  Progress,
  Avatar,
  Timeline,
  Modal,
  Form,
  Input,
  message,
  Badge,
  Tooltip,
  Tabs,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ToolOutlined,
  AlertOutlined,
  CalendarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { planTaskService, executionService } from '../../services/business';
import { PlanTask, ExecutionLog } from '../../types';
import { useApi } from '../../hooks/useApi';
import { authService } from '../../services/auth';
import RealTimeData from '../../components/Common/RealTimeData';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const MyTasks: React.FC = () => {
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'start' | 'pause' | 'resume' | 'complete' | 'issue'>('start');
  const [selectedTask, setSelectedTask] = useState<PlanTask | null>(null);
  const [form] = Form.useForm();

  const currentUser = authService.getCurrentUser();

  // 获取我的任务
  const {
    data: myTasks,
    loading,
    refresh: refreshTasks,
  } = useApi(
    () => planTaskService.getMyTasks(),
    { immediate: true }
  );

  // 模拟我的任务数据
  const mockMyTasks: PlanTask[] = [
    {
      id: 1,
      work_order_id: 1,
      routing_id: 1,
      step_number: 1,
      process_name: '原料准备',
      assigned_user_id: 1,
      machine_id: 1,
      status: 'IN_PROGRESS',
      planned_start: '2024-01-01T08:00:00Z',
      planned_end: '2024-01-01T10:00:00Z',
      actual_start: '2024-01-01T08:15:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T08:15:00Z',
    },
    {
      id: 2,
      work_order_id: 2,
      routing_id: 2,
      step_number: 2,
      process_name: 'CNC加工',
      assigned_user_id: 1,
      machine_id: 2,
      status: 'PLANNED',
      planned_start: '2024-01-01T10:00:00Z',
      planned_end: '2024-01-01T14:00:00Z',
      standard_hours: 4,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 3,
      work_order_id: 3,
      routing_id: 3,
      step_number: 1,
      process_name: '质量检测',
      assigned_user_id: 1,
      status: 'COMPLETED',
      planned_start: '2024-01-01T06:00:00Z',
      planned_end: '2024-01-01T07:00:00Z',
      actual_start: '2024-01-01T06:00:00Z',
      actual_end: '2024-01-01T06:45:00Z',
      standard_hours: 1,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T06:45:00Z',
    },
  ];

  const tasks = myTasks || mockMyTasks;

  // 刷新任务数据
  const refreshTasks = async () => {
    try {
      // 这里可以调用API获取真实数据
      // const response = await executionService.getMyTasks();
      // setMyTasks(response.data || []);
      console.log('刷新我的任务数据');
    } catch (error) {
      message.error('获取任务数据失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return '待开始';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const handleTaskAction = (task: PlanTask, action: typeof actionType) => {
    setSelectedTask(task);
    setActionType(action);
    form.resetFields();
    setActionModalVisible(true);
  };

  const handleActionSubmit = async (values: any) => {
    if (!selectedTask) return;

    try {
      const { notes, quality_data } = values;

      switch (actionType) {
        case 'start':
          await executionService.startTask(selectedTask.id, notes);
          message.success('任务已开始');
          break;
        case 'pause':
          await executionService.pauseTask(selectedTask.id, notes);
          message.success('任务已暂停');
          break;
        case 'resume':
          await executionService.resumeTask(selectedTask.id, notes);
          message.success('任务已恢复');
          break;
        case 'complete':
          await executionService.completeTask(selectedTask.id, notes, quality_data);
          message.success('任务已完成');
          break;
        case 'issue':
          await executionService.reportIssue(selectedTask.id, notes);
          message.success('问题已报告');
          break;
      }

      setActionModalVisible(false);
      refreshTasks();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const calculateTaskProgress = (task: PlanTask) => {
    if (task.status === 'COMPLETED') return 100;
    if (task.status === 'PLANNED') return 0;
    if (task.status === 'IN_PROGRESS' && task.actual_start) {
      const start = dayjs(task.actual_start);
      const end = dayjs(task.planned_end);
      const now = dayjs();
      const total = end.diff(start, 'minute');
      const elapsed = now.diff(start, 'minute');
      return Math.min(Math.max(Math.round((elapsed / total) * 100), 0), 100);
    }
    return 0;
  };

  // 统计数据
  const stats = {
    total: tasks.length,
    planned: tasks.filter(t => t.status === 'PLANNED').length,
    inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,
    completed: tasks.filter(t => t.status === 'COMPLETED').length,
  };

  // 按状态分组任务
  const plannedTasks = tasks.filter(t => t.status === 'PLANNED');
  const inProgressTasks = tasks.filter(t => t.status === 'IN_PROGRESS');
  const completedTasks = tasks.filter(t => t.status === 'COMPLETED');

  const renderTaskCard = (task: PlanTask) => (
    <Card
      key={task.id}
      size="small"
      style={{ marginBottom: '12px' }}
      actions={[
        task.status === 'PLANNED' && (
          <Tooltip title="开始任务">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(task, 'start')}
            >
              开始
            </Button>
          </Tooltip>
        ),
        task.status === 'IN_PROGRESS' && (
          <Tooltip title="暂停任务">
            <Button
              type="text"
              icon={<PauseCircleOutlined />}
              onClick={() => handleTaskAction(task, 'pause')}
            >
              暂停
            </Button>
          </Tooltip>
        ),
        task.status === 'IN_PROGRESS' && (
          <Tooltip title="完成任务">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleTaskAction(task, 'complete')}
            >
              完成
            </Button>
          </Tooltip>
        ),
        <Tooltip title="报告问题">
          <Button
            type="text"
            icon={<AlertOutlined />}
            danger
            onClick={() => handleTaskAction(task, 'issue')}
          >
            问题
          </Button>
        </Tooltip>,
      ].filter(Boolean)}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <Text strong>{task.process_name}</Text>
            <Tag color={getStatusColor(task.status)} style={{ marginLeft: '8px' }}>
              {getStatusText(task.status)}
            </Tag>
          </div>

          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text type="secondary">
              工单: WO-{task.work_order_id.toString().padStart(4, '0')} | 步骤: {task.step_number}
            </Text>

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <CalendarOutlined style={{ marginRight: '4px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {dayjs(task.planned_start).format('MM-DD HH:mm')} - {dayjs(task.planned_end).format('HH:mm')}
              </Text>
            </div>

            {task.machine_id && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ToolOutlined style={{ marginRight: '4px' }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  设备{task.machine_id}
                </Text>
              </div>
            )}

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <ClockCircleOutlined style={{ marginRight: '4px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                标准工时: {task.standard_hours}h
              </Text>
            </div>

            {task.status === 'IN_PROGRESS' && (
              <Progress
                percent={calculateTaskProgress(task)}
                size="small"
                status="active"
              />
            )}
          </Space>
        </div>
      </div>
    </Card>
  );

  return (
    <RealTimeData
      refreshInterval={15000} // 15秒刷新一次
      onRefresh={refreshTasks}
      showIndicator={true}
    >
      <div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <Space>
            <Avatar icon={<UserOutlined />} />
            <div>
              <Title level={2} style={{ margin: 0 }}>
                我的任务
              </Title>
              <Text type="secondary">
                {currentUser?.full_name || currentUser?.username}
              </Text>
            </div>
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总任务数"
                value={stats.total}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待开始"
                value={stats.planned}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="进行中"
                value={stats.inProgress}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已完成"
                value={stats.completed}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 任务列表 */}
        <Tabs
          defaultActiveKey="inProgress"
          items={[
            {
              key: 'inProgress',
              label: (
                <Badge count={inProgressTasks.length} offset={[10, 0]}>
                  <span>
                    <PlayCircleOutlined />
                    进行中
                  </span>
                </Badge>
              ),
              children: (
                <div style={{ minHeight: '400px' }}>
                  {inProgressTasks.length > 0 ? (
                    inProgressTasks.map(renderTaskCard)
                  ) : (
                    <Card style={{ textAlign: 'center', padding: '40px' }}>
                      <Text type="secondary">暂无进行中的任务</Text>
                    </Card>
                  )}
                </div>
              ),
            },
            {
              key: 'planned',
              label: (
                <Badge count={plannedTasks.length} offset={[10, 0]}>
                  <span>
                    <ClockCircleOutlined />
                    待开始
                  </span>
                </Badge>
              ),
              children: (
                <div style={{ minHeight: '400px' }}>
                  {plannedTasks.length > 0 ? (
                    plannedTasks.map(renderTaskCard)
                  ) : (
                    <Card style={{ textAlign: 'center', padding: '40px' }}>
                      <Text type="secondary">暂无待开始的任务</Text>
                    </Card>
                  )}
                </div>
              ),
            },
            {
              key: 'completed',
              label: (
                <Badge count={completedTasks.length} offset={[10, 0]}>
                  <span>
                    <CheckCircleOutlined />
                    已完成
                  </span>
                </Badge>
              ),
              children: (
                <div style={{ minHeight: '400px' }}>
                  {completedTasks.length > 0 ? (
                    <Timeline
                      items={completedTasks.map((task) => ({
                        color: 'green',
                        children: (
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{task.process_name}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              工单: WO-{task.work_order_id.toString().padStart(4, '0')} | 步骤: {task.step_number}
                            </div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              完成时间: {task.actual_end ? dayjs(task.actual_end).format('MM-DD HH:mm') : '-'}
                            </div>
                          </div>
                        ),
                      }))}
                    />
                  ) : (
                    <Card style={{ textAlign: 'center', padding: '40px' }}>
                      <Text type="secondary">暂无已完成的任务</Text>
                    </Card>
                  )}
                </div>
              ),
            },
          ]}
        />

        {/* 操作模态框 */}
        <Modal
          title={
            actionType === 'start' ? '开始任务' :
            actionType === 'pause' ? '暂停任务' :
            actionType === 'resume' ? '恢复任务' :
            actionType === 'complete' ? '完成任务' : '报告问题'
          }
          open={actionModalVisible}
          onCancel={() => setActionModalVisible(false)}
          footer={null}
          width={500}
        >
          {selectedTask && (
            <div style={{ marginBottom: '16px', padding: '12px', background: '#f5f5f5', borderRadius: '6px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text><strong>工序:</strong> {selectedTask.process_name}</Text>
                <Text><strong>工单:</strong> WO-{selectedTask.work_order_id.toString().padStart(4, '0')}</Text>
                <Text><strong>步骤:</strong> {selectedTask.step_number}</Text>
                <Text><strong>标准工时:</strong> {selectedTask.standard_hours}h</Text>
              </Space>
            </div>
          )}

          <Form
            form={form}
            layout="vertical"
            onFinish={handleActionSubmit}
          >
            <Form.Item
              name="notes"
              label="备注说明"
              rules={actionType === 'issue' ? [{ required: true, message: '请输入问题描述' }] : []}
            >
              <TextArea
                rows={4}
                placeholder={
                  actionType === 'start' ? '请输入开始备注...' :
                  actionType === 'pause' ? '请输入暂停原因...' :
                  actionType === 'resume' ? '请输入恢复说明...' :
                  actionType === 'complete' ? '请输入完成说明...' : '请详细描述遇到的问题...'
                }
              />
            </Form.Item>

            {actionType === 'complete' && (
              <Form.Item
                name="quality_data"
                label="质量数据"
              >
                <TextArea
                  rows={3}
                  placeholder="请输入质量检测数据..."
                />
              </Form.Item>
            )}

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  确认
                </Button>
                <Button onClick={() => setActionModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </RealTimeData>
  );
};

export default MyTasks;
