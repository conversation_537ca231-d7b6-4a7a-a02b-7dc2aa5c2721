/* MES系统通用样式 */

/* 页面布局 */
.page-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0 !important;
  color: #262626;
  font-weight: 600;
}

/* 卡片样式 */
.stats-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.filter-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.content-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 表格样式 */
.custom-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.custom-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.custom-table .ant-table-tbody > tr.row-in-progress > td {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
}

.custom-table .ant-table-tbody > tr.row-completed > td {
  background: #f0f0f0;
  color: #8c8c8c;
}

.custom-table .ant-table-tbody > tr.row-urgent > td {
  background: #fff2e8;
  border-left: 3px solid #fa8c16;
}

/* 状态标签 */
.status-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

.priority-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮样式 */
.action-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.primary-button {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
}

.primary-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 统计数字样式 */
.statistic-value {
  font-weight: 700;
  font-size: 24px;
}

.statistic-title {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
}

/* 进度条样式 */
.custom-progress .ant-progress-bg {
  border-radius: 4px;
}

.custom-progress .ant-progress-success-bg {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

.custom-progress .ant-progress-bg {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
}

/* 模态框样式 */
.custom-modal .ant-modal-header {
  border-radius: 8px 8px 0 0;
  background: linear-gradient(135deg, #f0f2f5 0%, #fafafa 100%);
}

.custom-modal .ant-modal-title {
  font-weight: 600;
  color: #262626;
}

.custom-modal .ant-modal-content {
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 表单样式 */
.custom-form .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.custom-form .ant-input,
.custom-form .ant-select-selector,
.custom-form .ant-picker {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.custom-form .ant-input:focus,
.custom-form .ant-select-focused .ant-select-selector,
.custom-form .ant-picker-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 搜索框样式 */
.search-input .ant-input-search-button {
  border-radius: 0 6px 6px 0;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
}

.search-input .ant-input-search-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border-color: #40a9ff;
}

/* 筛选器样式 */
.filter-row {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.filter-item {
  margin-bottom: 8px;
}

/* 工具提示样式 */
.custom-tooltip .ant-tooltip-inner {
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}

/* 标签样式 */
.custom-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
  border: none;
}

/* 徽章样式 */
.custom-badge .ant-badge-count {
  border-radius: 10px;
  font-weight: 600;
  font-size: 12px;
}

/* 分页样式 */
.custom-pagination .ant-pagination-item {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.custom-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
}

.custom-pagination .ant-pagination-item-active a {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stats-card {
    margin-bottom: 16px;
  }
  
  .filter-row {
    padding: 12px;
  }
  
  .custom-table .ant-table-thead > tr > th,
  .custom-table .ant-table-tbody > tr > td {
    padding: 8px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .statistic-value {
    font-size: 20px;
  }
  
  .action-button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px;
  background: #fafafa;
  border-radius: 8px;
}

.loading-text {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 14px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 50px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 16px;
}

.empty-description {
  font-size: 14px;
  color: #bfbfbf;
}

/* 实时数据指示器 */
.real-time-indicator {
  position: fixed;
  top: 80px;
  right: 24px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #f0f0f0;
}

.real-time-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #52c41a;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}
