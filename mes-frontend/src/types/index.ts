// 通用API响应类型 - 与后端保持一致
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
}

// 健康检查响应类型
export interface HealthStatus {
  status: string;
  timestamp: string;
  version: string;
}

// 用户相关类型 - 与后端UserInfo保持一致
export interface UserInfo {
  id: number;
  username: string;
  full_name: string;
  roles: string[];
}

// 兼容旧的User类型
export interface User extends UserInfo {
  skills?: string[];
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  email?: string;
  role?: string; // 兼容单个角色字段
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: UserInfo;
}

// 项目相关类型 - 与后端API保持一致
export interface Project {
  id: number;
  project_name: string;
  customer_name?: string;
  created_at: string;
  // 扩展字段 - 前端使用，后端可能不返回
  description?: string;
  status?: 'PLANNING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  start_date?: string;
  end_date?: string;
  updated_at?: string;
}

export interface ProjectWithBom {
  id: number;
  project_name: string;
  customer_name?: string;
  created_at: string;
  description?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
  bom_items: ProjectBomItem[];
}

export interface ProjectBomItem {
  id: number;
  part_id: number;
  part_number: string;
  part_name?: string;
  version: string;
  quantity: number;
  specifications?: string;
}

// 零件相关类型
export interface Part {
  id: number;
  part_number: string;
  part_name?: string;
  name?: string; // 兼容字段
  version?: string;
  specifications?: string;
  description?: string;
  standard_cost?: number;
  unit?: string;
  category?: string; // 前端使用的分类字段
  created_at?: string;
  updated_at?: string;
}

// 工单相关类型 - 与后端API保持一致
export interface WorkOrder {
  id: number;
  part_id?: number;
  project_bom_id?: number;
  project_id?: number; // 后端返回的字段
  quantity: number;
  status: 'PENDING' | 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  due_date?: string;
  created_at?: string;
  updated_at?: string;
  // 后端实际返回的字段
  project_name?: string;
  part_number?: string;
  part_name?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  planned_start?: string;
  planned_end?: string;
  actual_start?: string;
  actual_end?: string;
}

export interface WorkOrderWithDetails extends WorkOrder {
  part_version?: string;
  plan_tasks: WorkOrderPlanTask[];
}

export interface WorkOrderPlanTask {
  id: number;
  routing_step_id?: number;
  routing_id?: number; // 后端字段
  step_number: number;
  process_name: string;
  skill_group_name?: string;
  planned_start?: string;
  planned_end?: string;
  status: string;
  work_order_id: number;
  assigned_user_id?: number;
  machine_id?: number;
  standard_hours?: number;
  actual_start?: string;
  actual_end?: string;
  created_at?: string;
  updated_at?: string;
}

// 计划任务类型 - 与WorkOrderPlanTask保持一致
export interface PlanTask {
  id: number;
  work_order_id: number;
  routing_step_id?: number;
  routing_id?: number; // 后端字段
  skill_group_id?: number;
  planned_start: string;
  planned_end: string;
  status: 'PLANNED' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  // 扩展字段
  step_number?: number;
  process_name?: string;
  assigned_user_id?: number;
  machine_id?: number;
  standard_hours?: number;
  actual_start?: string;
  actual_end?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PlanTaskWithDetails extends PlanTask {
  skill_group_name?: string;
  project_name?: string;
  part_number?: string;
  part_name?: string;
  work_order_quantity?: number;
}

// 执行日志类型
export interface ExecutionLog {
  id: number;
  plan_task_id: number;
  machine_id?: number;
  user_id: number;
  event_type: 'START' | 'PAUSE' | 'RESUME' | 'COMPLETE' | 'QUALITY_CHECK' | 'ISSUE';
  event_time: string;
  notes?: string;
}

// 分页查询参数
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  list?: T[]; // 兼容字段
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// 统计数据类型
export interface DashboardStats {
  total_projects: number;
  active_projects: number;
  total_work_orders: number;
  active_work_orders: number;
  completed_tasks_today: number;
  active_operators: number;
  equipment_utilization: number;
  quality_rate: number;
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  title: string;
  icon?: string;
  children?: RouteConfig[];
  roles?: string[];
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
  roles?: string[];
}

// 表格列配置
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, record: any, index: number) => React.ReactNode;
  sorter?: boolean | ((a: any, b: any) => number);
  filters?: { text: string; value: any }[];
  onFilter?: (value: any, record: any) => boolean;
}

// 表单字段配置
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'date' | 'number' | 'switch' | 'upload';
  required?: boolean;
  options?: { label: string; value: any }[];
  placeholder?: string;
  rules?: any[];
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string;
  borderRadius: number;
  colorBgContainer: string;
}
