#!/usr/bin/env node

/**
 * MCP调试测试脚本
 * 用于测试远程MES系统的MCP浏览器调试功能
 */

const http = require('http');
const { spawn } = require('child_process');

// 配置
const CONFIG = {
  SERVER_IP: '************',
  BACKEND_PORT: 3000,
  FRONTEND_PORT: 3001,
  DEBUG_PORT: 9222,
  TEST_TIMEOUT: 30000
};

console.log('🔧 MES系统 MCP调试测试');
console.log('='.repeat(50));
console.log(`远程服务器: ${CONFIG.SERVER_IP}`);
console.log(`后端端口: ${CONFIG.BACKEND_PORT}`);
console.log(`前端端口: ${CONFIG.FRONTEND_PORT}`);
console.log(`调试端口: ${CONFIG.DEBUG_PORT}`);
console.log('='.repeat(50));

// 测试远程MES后端
async function testMESBackend() {
  console.log('\n🔍 1. 测试MES后端连接...');
  
  try {
    const response = await fetch(`http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}/api/health`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ MES后端连接成功');
      console.log(`   状态: ${response.status}`);
      console.log(`   响应: ${data.message || 'OK'}`);
      console.log(`   时间: ${data.timestamp || new Date().toISOString()}`);
      return true;
    } else {
      console.log(`❌ MES后端响应错误: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ MES后端连接失败: ${error.message}`);
    return false;
  }
}

// 测试React前端
async function testReactFrontend() {
  console.log('\n🔍 2. 测试React前端连接...');
  
  try {
    const response = await fetch(`http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`, {
      method: 'HEAD'
    });
    
    if (response.ok) {
      console.log('✅ React前端连接成功');
      console.log(`   状态: ${response.status}`);
      console.log(`   类型: ${response.headers.get('content-type') || 'text/html'}`);
      return true;
    } else {
      console.log(`❌ React前端响应错误: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ React前端连接失败: ${error.message}`);
    return false;
  }
}

// 测试Chrome调试端口
function testChromeDebug() {
  return new Promise((resolve, reject) => {
    console.log('\n🔍 3. 测试Chrome调试端口...');
    
    const req = http.get(`http://localhost:${CONFIG.DEBUG_PORT}/json`, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const tabs = JSON.parse(data);
          console.log(`✅ Chrome调试连接成功`);
          console.log(`   发现标签页: ${tabs.length} 个`);
          
          // 查找MES系统相关标签页
          const mesTabs = tabs.filter(tab => 
            tab.url && (
              tab.url.includes(`${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}`) ||
              tab.url.includes(`${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`)
            )
          );
          
          if (mesTabs.length > 0) {
            console.log(`✅ 找到MES系统标签页: ${mesTabs.length} 个`);
            mesTabs.forEach((tab, index) => {
              console.log(`   ${index + 1}. ${tab.title || 'Untitled'}`);
              console.log(`      URL: ${tab.url}`);
              console.log(`      WebSocket: ${tab.webSocketDebuggerUrl ? '可用' : '不可用'}`);
            });
          } else {
            console.log('⚠️  未找到MES系统标签页');
            console.log('   请在Chrome中打开以下地址:');
            console.log(`   - http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}`);
            console.log(`   - http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`);
          }
          
          resolve({ tabs, mesTabs });
        } catch (error) {
          reject(new Error(`解析调试信息失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`Chrome调试端口连接失败: ${error.message}`));
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('连接超时 - 请确保Chrome调试模式已启动'));
    });
  });
}

// 测试MCP浏览器服务器
function testMCPServer() {
  return new Promise((resolve, reject) => {
    console.log('\n🔍 4. 测试MCP浏览器服务器...');
    
    // 检查MCP包是否安装
    try {
      require.resolve('@modelcontextprotocol/server-browser');
      console.log('✅ MCP浏览器包已安装');
    } catch (error) {
      console.log('❌ MCP浏览器包未安装');
      console.log('   请运行: npm install @modelcontextprotocol/server-browser');
      reject(new Error('MCP包未安装'));
      return;
    }
    
    // 启动MCP服务器测试
    const mcpServer = spawn('node', [
      '-e',
      `
      const { BrowserServer } = require('@modelcontextprotocol/server-browser');
      console.log('MCP服务器启动测试...');
      setTimeout(() => {
        console.log('MCP服务器测试完成');
        process.exit(0);
      }, 3000);
      `
    ], {
      env: {
        ...process.env,
        BROWSER_WS_ENDPOINT: `ws://localhost:${CONFIG.DEBUG_PORT}`
      }
    });
    
    let output = '';
    let hasOutput = false;
    
    mcpServer.stdout.on('data', (data) => {
      output += data.toString();
      hasOutput = true;
      console.log(`   MCP: ${data.toString().trim()}`);
    });
    
    mcpServer.stderr.on('data', (data) => {
      console.log(`   MCP错误: ${data.toString().trim()}`);
    });
    
    mcpServer.on('close', (code) => {
      if (code === 0 && hasOutput) {
        console.log('✅ MCP服务器测试成功');
        resolve(true);
      } else {
        reject(new Error(`MCP服务器测试失败，退出码: ${code}`));
      }
    });
    
    mcpServer.on('error', (error) => {
      reject(new Error(`MCP服务器错误: ${error.message}`));
    });
    
    // 超时处理
    setTimeout(() => {
      if (!mcpServer.killed) {
        mcpServer.kill();
        if (hasOutput) {
          console.log('✅ MCP服务器测试成功 (超时但有输出)');
          resolve(true);
        } else {
          reject(new Error('MCP服务器测试超时'));
        }
      }
    }, 5000);
  });
}

// 生成MCP调试命令示例
function generateMCPCommands() {
  console.log('\n📋 5. MCP调试命令示例');
  console.log('-'.repeat(30));
  
  const commands = [
    {
      name: '导航到MES后端',
      command: `browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}");`
    },
    {
      name: '导航到React前端',
      command: `browser.navigate("http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}");`
    },
    {
      name: '点击健康检查按钮',
      command: `browser.click("button[onclick='testHealth()']");`
    },
    {
      name: '点击登录测试按钮',
      command: `browser.click("button[onclick='testLogin()']");`
    },
    {
      name: '截图',
      command: `browser.screenshot();`
    },
    {
      name: '获取页面内容',
      command: `browser.getContent();`
    },
    {
      name: '等待元素出现',
      command: `browser.waitForElement("#results");`
    },
    {
      name: '输入用户名',
      command: `browser.type("#username", "admin");`
    },
    {
      name: '输入密码',
      command: `browser.type("#password", "admin123");`
    },
    {
      name: '执行JavaScript',
      command: `browser.evaluate("document.title");`
    }
  ];
  
  commands.forEach((cmd, index) => {
    console.log(`${index + 1}. ${cmd.name}:`);
    console.log(`   ${cmd.command}`);
    console.log('');
  });
}

// 主测试流程
async function runTests() {
  const startTime = Date.now();
  
  try {
    console.log('🚀 开始测试...\n');
    
    // 测试MES后端
    const backendOk = await testMESBackend();
    
    // 测试React前端
    const frontendOk = await testReactFrontend();
    
    // 测试Chrome调试
    const debugResult = await testChromeDebug();
    
    // 测试MCP服务器
    const mcpOk = await testMCPServer();
    
    // 生成调试命令
    generateMCPCommands();
    
    // 总结
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log('\n🎉 测试完成！');
    console.log('='.repeat(50));
    console.log(`✅ MES后端: ${backendOk ? '正常' : '异常'}`);
    console.log(`✅ React前端: ${frontendOk ? '正常' : '异常'}`);
    console.log(`✅ Chrome调试: 正常 (${debugResult.tabs.length} 个标签页)`);
    console.log(`✅ MCP服务器: ${mcpOk ? '正常' : '异常'}`);
    console.log(`⏱️  测试耗时: ${duration}秒`);
    
    console.log('\n📱 访问地址:');
    console.log(`🌐 MES后端: http://${CONFIG.SERVER_IP}:${CONFIG.BACKEND_PORT}`);
    console.log(`🌐 React前端: http://${CONFIG.SERVER_IP}:${CONFIG.FRONTEND_PORT}`);
    console.log(`🔧 Chrome调试: http://localhost:${CONFIG.DEBUG_PORT}`);
    
    console.log('\n🔑 测试账号:');
    console.log('用户名: admin');
    console.log('密码: admin123');
    
    console.log('\n🎯 下一步:');
    console.log('1. 在Chrome中访问MES系统');
    console.log('2. 使用MCP命令进行自动化测试');
    console.log('3. 查看调试信息和日志');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.log('\n🔧 故障排除:');
    console.log('1. 确保远程MES系统正在运行');
    console.log('2. 确保Chrome调试模式已启动');
    console.log('3. 检查网络连接到远程服务器');
    console.log('4. 验证端口访问权限');
    console.log('5. 检查MCP包安装状态');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  testMESBackend,
  testReactFrontend,
  testChromeDebug,
  testMCPServer,
  CONFIG
};
