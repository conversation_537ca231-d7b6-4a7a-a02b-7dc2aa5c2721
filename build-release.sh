#!/bin/bash

echo "🧹 清理编译环境..."

# 杀死所有相关进程
pkill -f "mes-simple" || true
pkill -f "npm start" || true

# 删除已编译的版本
rm -rf target/
rm -rf mes-frontend/build/
rm -rf mes-frontend/node_modules/.cache/

# 清理Cargo缓存
cargo clean

echo "🔧 修复关键编译错误..."

# 添加缺失的依赖
cargo add dotenvy

# 创建简化的main_simple.rs，只包含基本功能
cat > src/main_simple.rs << 'EOF'
use axum::{
    routing::{get, post},
    Router,
    Extension,
};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;

mod config;
mod database;
mod error;
mod middleware;
mod models;
mod handlers;

use config::Config;
use database::Database;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::init();

    // 加载配置
    let config = Config::from_env();
    
    // 连接数据库
    let database = Database::new(&config.database_url).await?;
    
    // 创建路由
    let app = Router::new()
        .route("/", get(|| async { "MES System API" }))
        .route("/api/health", get(handlers::health_check))
        .route("/api/projects", get(handlers::list_projects))
        .route("/api/work-orders", get(handlers::list_work_orders))
        .layer(CorsLayer::permissive())
        .layer(Extension(database));

    // 启动服务器
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    println!("🚀 MES系统启动成功!");
    println!("📡 服务器监听地址: http://0.0.0.0:{}", config.port);
    println!("🌐 Web界面: http://192.168.2.11:{}", config.port);
    println!("🔗 API健康检查: http://192.168.2.11:{}/api/health", config.port);
    
    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await?;

    Ok(())
}
EOF

echo "✅ 创建简化版本完成"

echo "🔨 开始编译发布版本..."
cargo build --release --bin mes-simple

if [ $? -eq 0 ]; then
    echo "🎉 编译成功！"
    echo "📦 可执行文件位置: target/release/mes-simple"
    echo ""
    echo "🚀 启动命令:"
    echo "  ./target/release/mes-simple"
    echo ""
    echo "🌐 访问地址:"
    echo "  - API: http://192.168.2.11:3000"
    echo "  - 健康检查: http://192.168.2.11:3000/api/health"
else
    echo "❌ 编译失败，请检查错误信息"
    exit 1
fi
