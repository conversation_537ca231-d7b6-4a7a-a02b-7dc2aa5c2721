#!/usr/bin/env node

/**
 * 测试前端到后端的连接状况
 */

const axios = require('axios');

const BACKEND_URL = 'http://************:3000/api';
const FRONTEND_URL = 'http://localhost:3001';

console.log('🔗 测试前端到后端连接状况...\n');

async function testConnection() {
  try {
    // 1. 测试后端健康状态
    console.log('1. 测试后端健康状态...');
    const healthResponse = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ 后端健康状态:', healthResponse.data);
    
    // 2. 测试项目API
    console.log('\n2. 测试项目API...');
    const projectsResponse = await axios.get(`${BACKEND_URL}/projects`);
    console.log('✅ 项目API响应:', {
      success: projectsResponse.data.success,
      dataLength: projectsResponse.data.data?.length || 0,
      message: projectsResponse.data.message
    });
    
    // 3. 测试工单API
    console.log('\n3. 测试工单API...');
    const workOrdersResponse = await axios.get(`${BACKEND_URL}/work-orders`);
    console.log('✅ 工单API响应:', {
      success: workOrdersResponse.data.success,
      dataLength: workOrdersResponse.data.data?.length || 0,
      message: workOrdersResponse.data.message
    });
    
    // 4. 测试前端页面
    console.log('\n4. 测试前端页面...');
    const frontendResponse = await axios.get(FRONTEND_URL);
    console.log('✅ 前端页面可访问，状态码:', frontendResponse.status);
    
    console.log('\n🎉 连接测试完成！');
    console.log('\n📋 测试结果总结:');
    console.log('- 后端API服务正常运行');
    console.log('- 前端开发服务器正常运行');
    console.log('- API接口可以正常响应');
    console.log('- 数据格式符合预期');
    
    console.log('\n🌐 访问地址:');
    console.log(`- 前端: ${FRONTEND_URL}`);
    console.log(`- 后端API: ${BACKEND_URL}`);
    console.log(`- 后端健康检查: ${BACKEND_URL}/health`);
    
    console.log('\n💡 下一步建议:');
    console.log('1. 在浏览器中访问前端页面');
    console.log('2. 检查前端是否能正确调用后端API');
    console.log('3. 测试各个功能模块');
    console.log('4. 检查浏览器控制台是否有错误');
    
  } catch (error) {
    console.error('❌ 连接测试失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 可能的解决方案:');
      console.log('- 检查后端服务是否正在运行');
      console.log('- 检查端口是否被占用');
      console.log('- 检查防火墙设置');
    }
    
    if (error.response) {
      console.log('响应状态码:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
  }
}

testConnection();
