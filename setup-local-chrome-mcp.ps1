# 本地Chrome MCP调试环境一键安装脚本
# 用于连接远程MES系统 (************)

param(
    [string]$ServerIP = "************",
    [int]$BackendPort = 3000,
    [int]$FrontendPort = 3001,
    [int]$DebugPort = 9222
)

Write-Host "=== 本地Chrome MCP调试环境安装 ===" -ForegroundColor Green
Write-Host "远程服务器: $ServerIP" -ForegroundColor Yellow
Write-Host "后端端口: $BackendPort" -ForegroundColor Yellow
Write-Host "前端端口: $FrontendPort" -ForegroundColor Yellow
Write-Host "调试端口: $DebugPort" -ForegroundColor Yellow
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 创建工作目录
$WorkDir = "C:\chrome-debug-mes"
Write-Host "📁 创建工作目录: $WorkDir"
if (!(Test-Path $WorkDir)) {
    New-Item -ItemType Directory -Path $WorkDir -Force | Out-Null
}
Set-Location $WorkDir

# 检查Chrome安装
$ChromePaths = @(
    "C:\Program Files\Google\Chrome\Application\chrome.exe",
    "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
)

$ChromePath = $null
foreach ($path in $ChromePaths) {
    if (Test-Path $path) {
        $ChromePath = $path
        break
    }
}

if (!$ChromePath) {
    Write-Host "❌ 未找到Chrome浏览器" -ForegroundColor Red
    Write-Host "请先安装Google Chrome: https://www.google.com/chrome/" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "✅ 找到Chrome: $ChromePath" -ForegroundColor Green

# 检查Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js未安装"
    }
} catch {
    Write-Host "❌ Node.js未安装或版本过低" -ForegroundColor Red
    Write-Host "请安装Node.js 18+: https://nodejs.org/" -ForegroundColor Yellow
    pause
    exit 1
}

# 创建Chrome调试启动脚本
Write-Host "📝 创建Chrome调试启动脚本..."
$ChromeDebugScript = @"
@echo off
title Chrome MCP调试 - MES系统
echo ========================================
echo Chrome MCP调试启动器
echo 远程MES系统: http://$ServerIP`:$BackendPort
echo React前端: http://$ServerIP`:$FrontendPort  
echo 调试端口: http://localhost:$DebugPort
echo ========================================
echo.

echo 正在关闭现有Chrome进程...
taskkill /f /im chrome.exe 2>nul
timeout /t 2 /nobreak >nul

echo 启动Chrome调试模式...
"$ChromePath" ^
  --remote-debugging-port=$DebugPort ^
  --user-data-dir="%~dp0chrome-debug-data" ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --no-first-run ^
  --no-default-browser-check ^
  --new-window ^
  http://$ServerIP`:$BackendPort

echo.
echo Chrome调试模式已启动！
echo 调试信息: http://localhost:$DebugPort
echo.
pause
"@

$ChromeDebugScript | Out-File -FilePath "start-chrome-debug.bat" -Encoding ASCII

# 创建快速测试脚本
Write-Host "📝 创建快速测试脚本..."
$QuickTestScript = @"
@echo off
title MES系统连接测试
echo ========================================
echo MES系统连接测试
echo ========================================
echo.

echo 1. 测试远程MES后端连接...
curl -s -m 5 http://$ServerIP`:$BackendPort/api/health
if %errorlevel% == 0 (
    echo ✓ MES后端连接成功
) else (
    echo ✗ MES后端连接失败
)
echo.

echo 2. 测试React前端连接...
curl -s -m 5 -I http://$ServerIP`:$FrontendPort | find "200"
if %errorlevel% == 0 (
    echo ✓ React前端连接成功
) else (
    echo ✗ React前端连接失败
)
echo.

echo 3. 测试Chrome调试端口...
curl -s -m 5 http://localhost:$DebugPort/json > debug-info.json 2>nul
if %errorlevel% == 0 (
    echo ✓ Chrome调试端口连接成功
    echo 调试信息已保存到 debug-info.json
) else (
    echo ✗ Chrome调试端口连接失败
    echo 请先运行 start-chrome-debug.bat
)
echo.

echo 测试完成！
pause
"@

$QuickTestScript | Out-File -FilePath "test-connections.bat" -Encoding ASCII

# 安装MCP依赖
Write-Host "📦 安装MCP依赖包..."
if (!(Test-Path "package.json")) {
    npm init -y | Out-Null
}

Write-Host "正在安装 @modelcontextprotocol/server-browser..."
npm install @modelcontextprotocol/server-browser --save 2>$null

Write-Host "正在安装 axios..."
npm install axios --save 2>$null

# 创建MCP测试脚本
Write-Host "📝 创建MCP测试脚本..."
$MCPTestScript = @"
const http = require('http');
const { spawn } = require('child_process');

console.log('=== MES系统 Chrome MCP 调试测试 ===\n');

// 测试远程MES系统
async function testMESBackend() {
    console.log('🔍 测试MES后端连接...');
    try {
        const response = await fetch('http://$ServerIP`:$BackendPort/api/health');
        const data = await response.json();
        console.log('✅ MES后端连接成功');
        console.log('   响应:', data.message || 'OK');
        return true;
    } catch (error) {
        console.log('❌ MES后端连接失败:', error.message);
        return false;
    }
}

// 测试Chrome调试端口
function testChromeDebug() {
    return new Promise((resolve, reject) => {
        console.log('🔍 测试Chrome调试端口...');
        
        const req = http.get('http://localhost:$DebugPort/json', (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const tabs = JSON.parse(data);
                    console.log(`✅ Chrome调试连接成功，发现 `+tabs.length+` 个标签页`);
                    
                    const mesTab = tabs.find(tab => 
                        tab.url && tab.url.includes('$ServerIP`:$BackendPort')
                    );
                    
                    if (mesTab) {
                        console.log('✅ 找到MES系统标签页:', mesTab.title);
                    }
                    
                    resolve(tabs);
                } catch (error) {
                    reject(new Error('解析调试信息失败: ' + error.message));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error('Chrome调试端口连接失败: ' + error.message));
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('连接超时'));
        });
    });
}

// 主测试流程
async function runTests() {
    try {
        await testMESBackend();
        await testChromeDebug();
        
        console.log('\n🎉 测试完成！');
        console.log('\n📋 下一步操作:');
        console.log('1. MES后端: http://$ServerIP`:$BackendPort');
        console.log('2. React前端: http://$ServerIP`:$FrontendPort');
        console.log('3. Chrome调试: http://localhost:$DebugPort');
        console.log('4. 测试登录: admin/admin123');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        console.log('\n🔧 故障排除:');
        console.log('1. 确保远程MES系统正在运行');
        console.log('2. 确保Chrome调试模式已启动');
        console.log('3. 检查网络连接');
    }
}

runTests();
"@

$MCPTestScript | Out-File -FilePath "test-mcp-connection.js" -Encoding UTF8

# 创建桌面快捷方式
Write-Host "🔗 创建桌面快捷方式..."
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$ShortcutPath = "$DesktopPath\MES Chrome调试.lnk"

$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut($ShortcutPath)
$Shortcut.TargetPath = "$WorkDir\start-chrome-debug.bat"
$Shortcut.WorkingDirectory = $WorkDir
$Shortcut.Description = "启动Chrome MCP调试 - MES系统"
$Shortcut.Save()

# 完成安装
Write-Host ""
Write-Host "🎉 安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📁 安装目录: $WorkDir" -ForegroundColor Yellow
Write-Host "🖥️  桌面快捷方式: MES Chrome调试.lnk" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 使用步骤:" -ForegroundColor Cyan
Write-Host "1. 双击桌面快捷方式启动Chrome调试模式" -ForegroundColor White
Write-Host "2. 运行 test-connections.bat 测试连接" -ForegroundColor White
Write-Host "3. 运行 node test-mcp-connection.js 测试MCP" -ForegroundColor White
Write-Host "4. 在Chrome中访问 http://$ServerIP`:$BackendPort" -ForegroundColor White
Write-Host ""
Write-Host "📊 系统地址:" -ForegroundColor Cyan
Write-Host "• MES后端: http://$ServerIP`:$BackendPort" -ForegroundColor White
Write-Host "• React前端: http://$ServerIP`:$FrontendPort" -ForegroundColor White
Write-Host "• Chrome调试: http://localhost:$DebugPort" -ForegroundColor White
Write-Host ""

# 询问是否立即启动
$response = Read-Host "是否立即启动Chrome调试模式? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host "启动Chrome调试模式..." -ForegroundColor Green
    Start-Process -FilePath "$WorkDir\start-chrome-debug.bat"
}

Write-Host "安装脚本执行完成！" -ForegroundColor Green
pause
"@

$MCPTestScript | Out-File -FilePath "test-mcp-connection.js" -Encoding UTF8
