#!/usr/bin/env node

/**
 * 前后端集成测试脚本
 * 测试React前端与Rust后端的数据交互
 */

const http = require('http');
const { spawn } = require('child_process');

// 配置
const CONFIG = {
  BACKEND_URL: 'http://192.168.2.11:3000',
  FRONTEND_URL: 'http://192.168.2.11:3001',
  TEST_TIMEOUT: 30000
};

console.log('🧪 前后端集成测试');
console.log('='.repeat(50));
console.log(`后端地址: ${CONFIG.BACKEND_URL}`);
console.log(`前端地址: ${CONFIG.FRONTEND_URL}`);
console.log('='.repeat(50));

// 测试后端API
async function testBackendAPIs() {
  console.log('\n🔍 1. 测试后端API接口...');
  
  const tests = [
    {
      name: '健康检查',
      url: '/api/health',
      method: 'GET',
      expected: { success: true }
    },
    {
      name: '用户登录',
      url: '/api/auth/login',
      method: 'POST',
      data: { username: 'admin', password: 'admin123' },
      expected: { success: true }
    },
    {
      name: '项目列表',
      url: '/api/projects',
      method: 'GET',
      expected: { success: true }
    },
    {
      name: '工单列表',
      url: '/api/work-orders',
      method: 'GET',
      expected: { success: true }
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      console.log(`   测试: ${test.name}...`);
      
      const options = {
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
        }
      };

      let body = '';
      if (test.data) {
        body = JSON.stringify(test.data);
        options.headers['Content-Length'] = Buffer.byteLength(body);
      }

      const response = await fetch(`${CONFIG.BACKEND_URL}${test.url}`, {
        ...options,
        body: test.method === 'POST' ? body : undefined
      });

      const data = await response.json();
      
      if (response.ok && data.success === test.expected.success) {
        console.log(`   ✅ ${test.name}: 成功`);
        console.log(`      状态: ${response.status}`);
        console.log(`      响应: ${data.message || 'OK'}`);
        results.push({ test: test.name, status: 'success', data });
      } else {
        console.log(`   ❌ ${test.name}: 失败`);
        console.log(`      状态: ${response.status}`);
        console.log(`      响应: ${JSON.stringify(data, null, 2)}`);
        results.push({ test: test.name, status: 'failed', error: data });
      }
    } catch (error) {
      console.log(`   ❌ ${test.name}: 错误 - ${error.message}`);
      results.push({ test: test.name, status: 'error', error: error.message });
    }
  }

  return results;
}

// 测试前端页面
async function testFrontendPages() {
  console.log('\n🔍 2. 测试前端页面...');
  
  const pages = [
    { name: '主页', path: '/' },
    { name: '登录页', path: '/login' },
    { name: '项目列表', path: '/projects' },
    { name: '工单列表', path: '/work-orders' },
    { name: '执行看板', path: '/execution/board' }
  ];

  const results = [];

  for (const page of pages) {
    try {
      console.log(`   测试: ${page.name}...`);
      
      const response = await fetch(`${CONFIG.FRONTEND_URL}${page.path}`, {
        method: 'HEAD'
      });
      
      if (response.ok) {
        console.log(`   ✅ ${page.name}: 可访问`);
        console.log(`      状态: ${response.status}`);
        console.log(`      类型: ${response.headers.get('content-type') || 'text/html'}`);
        results.push({ page: page.name, status: 'success' });
      } else {
        console.log(`   ❌ ${page.name}: 不可访问`);
        console.log(`      状态: ${response.status}`);
        results.push({ page: page.name, status: 'failed', error: response.status });
      }
    } catch (error) {
      console.log(`   ❌ ${page.name}: 错误 - ${error.message}`);
      results.push({ page: page.name, status: 'error', error: error.message });
    }
  }

  return results;
}

// 测试前后端数据流
async function testDataFlow() {
  console.log('\n🔍 3. 测试前后端数据流...');
  
  try {
    // 1. 测试登录流程
    console.log('   测试登录流程...');
    const loginResponse = await fetch(`${CONFIG.BACKEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    
    const loginData = await loginResponse.json();
    
    if (loginData.success && loginData.data.token) {
      console.log('   ✅ 登录成功，获得Token');
      const token = loginData.data.token;
      
      // 2. 使用Token测试受保护的API
      console.log('   测试Token认证...');
      const protectedResponse = await fetch(`${CONFIG.BACKEND_URL}/api/projects`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const protectedData = await protectedResponse.json();
      
      if (protectedData.success) {
        console.log('   ✅ Token认证成功');
        console.log(`   📊 获取到 ${protectedData.data?.length || 0} 个项目`);
        
        // 3. 测试数据格式
        if (protectedData.data && Array.isArray(protectedData.data)) {
          console.log('   ✅ 数据格式正确 (Array)');
          
          if (protectedData.data.length > 0) {
            const project = protectedData.data[0];
            const requiredFields = ['id', 'project_name', 'created_at'];
            const hasAllFields = requiredFields.every(field => field in project);
            
            if (hasAllFields) {
              console.log('   ✅ 项目数据结构正确');
              console.log(`      示例项目: ${project.project_name}`);
            } else {
              console.log('   ⚠️  项目数据结构不完整');
              console.log(`      缺少字段: ${requiredFields.filter(f => !(f in project))}`);
            }
          }
        } else {
          console.log('   ❌ 数据格式错误 (非Array)');
        }
        
        return { status: 'success', token, projectCount: protectedData.data?.length || 0 };
      } else {
        console.log('   ❌ Token认证失败');
        return { status: 'failed', error: 'Token认证失败' };
      }
    } else {
      console.log('   ❌ 登录失败');
      return { status: 'failed', error: '登录失败' };
    }
  } catch (error) {
    console.log(`   ❌ 数据流测试错误: ${error.message}`);
    return { status: 'error', error: error.message };
  }
}

// 测试API响应时间
async function testPerformance() {
  console.log('\n🔍 4. 测试API性能...');
  
  const apis = [
    { name: '健康检查', url: '/api/health' },
    { name: '项目列表', url: '/api/projects' },
    { name: '工单列表', url: '/api/work-orders' }
  ];

  const results = [];

  for (const api of apis) {
    try {
      console.log(`   测试: ${api.name}...`);
      
      const start = Date.now();
      const response = await fetch(`${CONFIG.BACKEND_URL}${api.url}`);
      const end = Date.now();
      
      const responseTime = end - start;
      
      if (response.ok) {
        console.log(`   ✅ ${api.name}: ${responseTime}ms`);
        results.push({ api: api.name, responseTime, status: 'success' });
      } else {
        console.log(`   ❌ ${api.name}: 失败 (${response.status})`);
        results.push({ api: api.name, responseTime, status: 'failed' });
      }
    } catch (error) {
      console.log(`   ❌ ${api.name}: 错误 - ${error.message}`);
      results.push({ api: api.name, status: 'error', error: error.message });
    }
  }

  return results;
}

// 生成测试报告
function generateReport(backendResults, frontendResults, dataFlowResult, performanceResults) {
  console.log('\n📊 测试报告');
  console.log('='.repeat(50));
  
  // 后端API测试结果
  const backendSuccess = backendResults.filter(r => r.status === 'success').length;
  const backendTotal = backendResults.length;
  console.log(`🔧 后端API测试: ${backendSuccess}/${backendTotal} 通过`);
  
  // 前端页面测试结果
  const frontendSuccess = frontendResults.filter(r => r.status === 'success').length;
  const frontendTotal = frontendResults.length;
  console.log(`🌐 前端页面测试: ${frontendSuccess}/${frontendTotal} 通过`);
  
  // 数据流测试结果
  console.log(`🔄 数据流测试: ${dataFlowResult.status === 'success' ? '✅ 通过' : '❌ 失败'}`);
  
  // 性能测试结果
  const performanceSuccess = performanceResults.filter(r => r.status === 'success').length;
  const performanceTotal = performanceResults.length;
  const avgResponseTime = performanceResults
    .filter(r => r.responseTime)
    .reduce((sum, r) => sum + r.responseTime, 0) / performanceSuccess || 0;
  
  console.log(`⚡ 性能测试: ${performanceSuccess}/${performanceTotal} 通过`);
  console.log(`📈 平均响应时间: ${Math.round(avgResponseTime)}ms`);
  
  // 总体评估
  const totalTests = backendTotal + frontendTotal + 1 + performanceTotal;
  const totalSuccess = backendSuccess + frontendSuccess + 
    (dataFlowResult.status === 'success' ? 1 : 0) + performanceSuccess;
  
  console.log('\n🎯 总体评估:');
  console.log(`✅ 通过: ${totalSuccess}/${totalTests} (${Math.round(totalSuccess/totalTests*100)}%)`);
  
  if (totalSuccess === totalTests) {
    console.log('🎉 所有测试通过！前后端集成正常工作。');
  } else {
    console.log('⚠️  部分测试失败，请检查相关组件。');
  }
  
  // 建议
  console.log('\n💡 建议:');
  if (avgResponseTime > 1000) {
    console.log('- API响应时间较慢，考虑优化后端性能');
  }
  if (backendSuccess < backendTotal) {
    console.log('- 部分后端API失败，检查服务器状态');
  }
  if (frontendSuccess < frontendTotal) {
    console.log('- 部分前端页面不可访问，检查路由配置');
  }
  if (dataFlowResult.status !== 'success') {
    console.log('- 数据流测试失败，检查前后端通信');
  }
}

// 主测试流程
async function runTests() {
  const startTime = Date.now();
  
  try {
    console.log('🚀 开始前后端集成测试...\n');
    
    // 运行所有测试
    const backendResults = await testBackendAPIs();
    const frontendResults = await testFrontendPages();
    const dataFlowResult = await testDataFlow();
    const performanceResults = await testPerformance();
    
    // 生成报告
    generateReport(backendResults, frontendResults, dataFlowResult, performanceResults);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n⏱️  测试耗时: ${duration}秒`);
    console.log('\n🔗 访问地址:');
    console.log(`🌐 前端: ${CONFIG.FRONTEND_URL}`);
    console.log(`🔧 后端: ${CONFIG.BACKEND_URL}`);
    console.log(`📋 API文档: ${CONFIG.BACKEND_URL}/api/health`);
    
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  testBackendAPIs,
  testFrontendPages,
  testDataFlow,
  testPerformance,
  CONFIG
};
