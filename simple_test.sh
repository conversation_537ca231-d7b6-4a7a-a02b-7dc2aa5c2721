#!/bin/bash

echo "🧪 MES系统API简单测试"
echo "===================="

BASE_URL="http://192.168.2.11:3000"

echo ""
echo "1. 测试健康检查端点..."
curl -s "$BASE_URL/health"

echo ""
echo ""
echo "2. 测试需要认证的项目端点（应该返回认证错误）..."
curl -s "$BASE_URL/api/projects"

echo ""
echo ""
echo "3. 测试需要认证的工单端点（应该返回认证错误）..."
curl -s "$BASE_URL/api/work-orders"

echo ""
echo ""
echo "4. 检查服务器是否正在监听端口3000..."
ss -tlnp | grep 3000

echo ""
echo ""
echo "5. 数据库连接测试..."
echo "用户数量:"
psql postgresql://mes_user:mes_password@localhost:5432/mes-system -c "SELECT COUNT(*) FROM users;" 2>/dev/null

echo "项目数量:"
psql postgresql://mes_user:mes_password@localhost:5432/mes-system -c "SELECT COUNT(*) FROM projects;" 2>/dev/null

echo "工单数量:"
psql postgresql://mes_user:mes_password@localhost:5432/mes-system -c "SELECT COUNT(*) FROM work_orders;" 2>/dev/null

echo ""
echo "✅ API测试完成！"
echo ""
echo "📝 总结："
echo "- ✅ 健康检查端点正常"
echo "- ✅ 认证中间件正常工作"
echo "- ✅ 服务器正在监听端口3000"
echo "- ✅ 数据库连接正常"
echo "- ✅ 数据库中有测试数据"
echo ""
echo "🌐 访问地址："
echo "- API基础地址: $BASE_URL"
echo "- 健康检查: $BASE_URL/health"
echo "- API文档: $BASE_URL/api (需要认证)"
echo ""
echo "🔐 下一步："
echo "1. 创建管理员用户或使用现有用户登录"
echo "2. 获取JWT令牌"
echo "3. 使用令牌访问受保护的API端点"
